# -*- coding: utf-8 -*-
"""
LSTM-With-Exog  ——  SHAP 解释脚本
  • 生成 mean-|SHAP| 热力图（含 t0 行，Q 置空）
  • waterfall 示例
  • 保存 SHAP 原始矩阵 / 热力图 TXT / PNG
"""
import os, pickle, warnings
import numpy as np
import pandas as pd
import paddle
import paddle.nn as nn
import shap, seaborn as sns
import matplotlib.pyplot as plt

# ========= ① 路径与超参 ==========================================
DATA_DIR      = (r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903"
                 r"\预处理输出")
TEST_NPZ      = os.path.join(DATA_DIR, "test_look24.npz")
MODEL_WEIGHTS = os.path.join(DATA_DIR, "lstm_with_exog.pdparams")

LOOK_BACK   = 24
HIDDEN_SIZE = 128
NUM_LAYERS  = 2
EXOG_DIM    = 6                        # ← P1-P5 + Evap (t0)

CACHE_PKL   = os.path.join(DATA_DIR, f"shap_values_look{LOOK_BACK}.pkl")
FORCE_RECALC = False

BASE_VAR_NAMES = ["Q", "P1", "P2", "P3", "P4", "P5", "Evap"]
exog_names     = ["P1_t0", "P2_t0", "P3_t0", "P4_t0", "P5_t0", "Evap_t0"]
# ================================================================

paddle.set_device("gpu" if paddle.is_compiled_with_cuda() else "cpu")
warnings.filterwarnings("ignore", category=UserWarning, module="shap")

# ---------- 1. 数据加载 ------------------------------------------
npz     = np.load(TEST_NPZ)
X_hist  = npz["X_hist"].astype("float32")          # (N, 24, 7)
X_exog  = npz["X_exog"].astype("float32")          # (N, 6)
N, LB, F_in = X_hist.shape
assert LB == LOOK_BACK and X_exog.shape[1] == EXOG_DIM

X_flat   = np.concatenate([X_hist.reshape(N, -1), X_exog], axis=1)  # (N, 174)
FLAT_DIM = X_flat.shape[1]

# ---------- 2. 模型复现 & 加权 -----------------------------------
class LSTMWithExog(nn.Layer):
    def __init__(self, input_dim: int, hidden: int, layers: int, exog_dim: int = 6):
        super().__init__()
        self.lstm = nn.LSTM(input_dim, hidden, layers)
        self.fc   = nn.Sequential(
            nn.Linear(hidden + exog_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
    def forward(self, hist, exog):
        hist = paddle.transpose(hist, perm=[1, 0, 2])     # (seq,batch,feat)
        last = self.lstm(hist)[0][-1]                     # (B, hidden)
        return self.fc(paddle.concat([last, exog], 1)).squeeze(1)

model = LSTMWithExog(F_in, HIDDEN_SIZE, NUM_LAYERS, EXOG_DIM)
model.set_state_dict(paddle.load(str(MODEL_WEIGHTS)))
model.eval()
print("✔ 已加载权重：", MODEL_WEIGHTS)

# ---------- 3. 预测包装 ------------------------------------------
def predict(flat):
    """flat 可为 ndarray / DataFrame，返回 (batch,)"""
    if isinstance(flat, pd.DataFrame):
        flat = flat.to_numpy()
    flat = flat.astype("float32", copy=False)

    m     = flat.shape[0]
    hist  = flat[:, :LOOK_BACK*F_in].reshape(m, LOOK_BACK, F_in)
    exog  = flat[:, LOOK_BACK*F_in:]
    with paddle.no_grad():
        return model(paddle.to_tensor(hist), paddle.to_tensor(exog)).numpy()

# ---------- 4. 特征名 --------------------------------------------
feat_names = [f"{v}_t-{lag}"
              for lag in range(LOOK_BACK, 0, -1)
              for v in BASE_VAR_NAMES] + exog_names           # ← 新增
X_df          = pd.DataFrame(X_flat, columns=feat_names)
background_df = X_df.sample(100, random_state=0)

# ---------- 5. 计算 / 读取 SHAP -----------------------------------
if (not FORCE_RECALC) and os.path.exists(CACHE_PKL):
    with open(CACHE_PKL, "rb") as f:
        shap_values = pickle.load(f)
    print("✔ 已从缓存读取 SHAP：", CACHE_PKL)
else:
    print("• 正在计算 SHAP 值，请耐心等待…")
    explainer   = shap.Explainer(predict, background_df)
    shap_values = explainer(X_df)
    with open(CACHE_PKL, "wb") as f:
        pickle.dump(shap_values, f)
    print("✔ 已保存 SHAP 缓存：", CACHE_PKL)

# ---------- 6. 平均 |SHAP| 热图（含 t0 行） -----------------------
abs_mean = np.abs(shap_values.values).mean(0)      # (174,)

# 6-1 历史 24 行
hist_mat = abs_mean[:LOOK_BACK*F_in].reshape(LOOK_BACK, F_in)

# 6-2 追加 t0 行：Q 置 NaN，P1-P5,Evap = exog 的 |SHAP|
t0_row = np.full(F_in, np.nan, dtype="float32")
t0_row[1:] = abs_mean[-EXOG_DIM:]                  # 填 P1-P5,Evap

full_mat = np.vstack([hist_mat, t0_row])           # (25,7)
row_idx  = [f"t-{i}" for i in range(LOOK_BACK, 0, -1)] + ["t0"]

heat_df = pd.DataFrame(full_mat, index=row_idx, columns=BASE_VAR_NAMES)

plt.figure(figsize=(1.2*F_in, 0.7*(LOOK_BACK+1)+1))
sns.heatmap(heat_df, annot=True, fmt=".4f", cmap="Reds",
            cbar_kws={"label": "Mean |SHAP|"})
plt.title("Mean |SHAP|  (larger ⇒ stronger impact)")
plt.tight_layout()
png_heat = os.path.join(DATA_DIR, "shap_heatmap.png")
plt.savefig(png_heat, dpi=300)
print("✔ 热图已保存：", png_heat)

# 6-3 保存 TXT
txt_heat = os.path.join(DATA_DIR, "shap_mean_abs_matrix.txt")
heat_df.to_csv(txt_heat, sep="\t", float_format="%.6f", na_rep="")
print("✔ TXT 已保存：", txt_heat)

# ---------- 7. waterfall 示例 ------------------------------------
idx_show = 0
print(f"• 绘制样本 idx={idx_show} 的 waterfall …")
plt.figure(figsize=(7, 5))
shap.plots.waterfall(shap_values[idx_show], max_display=20, show=True)
plt.tight_layout()
png_wf = os.path.join(DATA_DIR, f"shap_waterfall_idx{idx_show}.png")
plt.savefig(png_wf, dpi=300)
print("✔ waterfall 已保存：", png_wf)

# ---------- 8. 另存 SHAP 原矩阵 ----------------------------------
raw_txt = os.path.join(DATA_DIR, "shap_raw_values.txt")
np.savetxt(raw_txt, shap_values.values, fmt="%.6e", delimiter="\t")
print("✔ shap_values 全矩阵已保存：", raw_txt)
