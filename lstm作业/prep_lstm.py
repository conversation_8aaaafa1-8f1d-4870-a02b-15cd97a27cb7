# -*- coding: utf-8 -*-
"""
生成 LSTM 训练 / 测试数据 + scaler.pkl + meta.json
------------------------------------------------
* look_back = 24  （覆盖汇流历时）
* 外生量改为：t0 时刻 “5 站雨量 + 蒸发”  共 6 维
* 输出：
    ├── train_look24.npz :  X_hist(…,24,F)   X_exog(…,6)   y(…)
    ├── test_look24.npz
    ├── minmax_scaler.pkl
    └── meta.json
"""

import json, pickle, warnings
from pathlib import Path

import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split

warnings.simplefilter('ignore', category=FutureWarning)

# ========================== ★ 路径在此修改 ==========================
BASE_DIR = Path(r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903")
xls_path = BASE_DIR / "安徽中小流域资料整理 - 给孟涵 - 20230903" / "月潭次模率定.xls"
out_dir  = BASE_DIR / "预处理输出"
out_dir.mkdir(parents=True, exist_ok=True)
# ===================================================================

LOOK_BACK   = 24               # 历史窗口长度
TARGET_IDX  = 0                # 预测目标列（流量列在 feature_df 中的索引）
TEST_RATIO  = 0.20             # 测试集比例

# ---------- 1. 读取三张 Sheet ----------
summary_df = pd.read_excel(xls_path, sheet_name="summary", engine="xlrd")
data_df    = pd.read_excel(xls_path, sheet_name="data",    engine="xlrd")
evap_df    = pd.read_excel(xls_path, sheet_name="evapd",   engine="xlrd")

def int2dt(series: pd.Series, unit="H"):
    """把整数日期/时间列转为 pd.Timestamp"""
    fmt = "%Y%m%d%H" if unit.upper() == "H" else "%Y%m%d"
    return pd.to_datetime(series.astype(str).str.zfill(len(fmt)-2), format=fmt)

data_df["ts"]   = int2dt(data_df.iloc[:, 0], "H")
evap_df["date"] = int2dt(evap_df.iloc[:, 0], "D")
data_df.set_index("ts", inplace=True)
evap_df.set_index("date", inplace=True)

# 蒸发（日值 → 小时）
evap_df.drop(columns=evap_df.columns[0], inplace=True)
evap_h = (
    evap_df
    .resample("h").ffill()
    .rename(columns={evap_df.columns[0]: "evap"})
)

# 按列顺序：流量、5 站雨量、蒸发  ==> 总特征 F = 7
flow_col  = data_df.columns[1]     # 流量
rain_cols = data_df.columns[2:]    # 5 站雨量
feature_df = data_df[[flow_col, *rain_cols]].join(evap_h["evap"], how="left")

# ---------- 2. 按场次切片 ----------
events = {}
for _, row in summary_df.iterrows():
    eid = int(row.iloc[0])
    st  = pd.to_datetime(str(int(row.iloc[1])), format="%Y%m%d%H")
    ed  = pd.to_datetime(str(int(row.iloc[3])), format="%Y%m%d%H")
    seg = feature_df.loc[st:ed]
    if not seg.empty:
        # 线性插值补缺
        events[eid] = seg.interpolate("linear").ffill().bfill().astype("float32")

# ---------- 3. 全局 MinMax 归一化 ----------
all_mat = np.concatenate([df.values for df in events.values()], axis=0)
scaler  = MinMaxScaler().fit(all_mat)
events_scaled = {
    eid: pd.DataFrame(
        scaler.transform(df.values), index=df.index, columns=df.columns
    )
    for eid, df in events.items()
}

# ---------- 4. 生成监督学习样本 ----------
Xs_hist, Xs_exog, ys = [], [], []
for eid, df in events_scaled.items():
    mat = df.values                     # shape:(T, 7)
    for i in range(len(mat) - LOOK_BACK):
        hist  = mat[i : i + LOOK_BACK, :]        # 24 × 7  —— t-23 … t0
        exog = mat[i+LOOK_BACK, 1:]       # t0 时刻的 P1–P5+Evap  (6,)
        y     = mat[i + LOOK_BACK, TARGET_IDX]   # t+1 时刻流量（监督目标）
        Xs_hist.append(hist)
        Xs_exog.append(exog)
        ys.append(y)

# 转 ndarray
X_hist = np.stack(Xs_hist).astype("float32")     # (N, 24, 7)
X_exog = np.stack(Xs_exog).astype("float32")     # (N, 6)
y      = np.asarray(ys, dtype="float32")         # (N,)
print(f"✧ 样本量 {len(y):,}  |  X_hist={X_hist.shape}  X_exog={X_exog.shape}")

# ---------- 5. 训练 / 测试切分并保存 ----------
Xh_tr, Xh_te, Xe_tr, Xe_te, y_tr, y_te = train_test_split(
    X_hist, X_exog, y,
    test_size=TEST_RATIO,
    shuffle=True,
    random_state=42
)

np.savez_compressed(out_dir / "train_look24.npz",
                    X_hist=Xh_tr, X_exog=Xe_tr, y=y_tr)
np.savez_compressed(out_dir / "test_look24.npz",
                    X_hist=Xh_te, X_exog=Xe_te, y=y_te)
with open(out_dir / "minmax_scaler.pkl", "wb") as f:
    pickle.dump(scaler, f)

print("✔ 数据文件 & scaler 已保存至：", out_dir)

# ---------- 6. 写 meta.json ----------
meta = {
    "look_back"  : LOOK_BACK,
    "input_dim"  : X_hist.shape[-1],   # 7
    "exog_dim"   : 6,                  # P1–P5 + Evap
    "hidden_size": 132,                # 你打算用的 LSTM 隐层
    "num_layers" : 2
}
with open(out_dir / "meta.json", "w", encoding="utf-8") as f:
    json.dump(meta, f, indent=2, ensure_ascii=False)

print("✔ meta.json 已写入：", out_dir / "meta.json")
