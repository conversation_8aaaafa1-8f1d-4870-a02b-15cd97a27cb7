# -*- coding: utf-8 -*-
"""
convert_shap_to_m3s.py
-----------------------------------------------------------
读取  shap_mean_abs_matrix.txt  +  minmax_scaler.pkl
→  将 |SHAP| 值乘以 ΔQ (m³/s)        # 目标列在第 0 列
→  保存:
      shap_mean_abs_matrix_m3s.txt
      shap_heatmap_m3s.png
"""

import pickle, os
import numpy as np
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

# ======== ★ 路径（按需修改） =========================================
BASE_DIR  = r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903"
OUT_DIR   = os.path.join(BASE_DIR, "预处理输出")

TXT_IN    = os.path.join(OUT_DIR, "shap_mean_abs_matrix.txt")
PKL_SCAL  = os.path.join(OUT_DIR, "minmax_scaler.pkl")

TXT_OUT   = os.path.join(OUT_DIR, "shap_mean_abs_matrix_m3s.txt")
PNG_OUT   = os.path.join(OUT_DIR, "shap_heatmap_m3s.png")
# ====================================================================

# ---------- 1. 读取 SHAP 矩阵 ----------
shap_df = pd.read_csv(TXT_IN, sep=r"\t", index_col=0)   # 行索引=t-24…t0

# ---------- 2. 读取 scaler，计算 ΔQ ----------
with open(PKL_SCAL, "rb") as f:
    scaler = pickle.load(f)

delta_Q = scaler.data_max_[0] - scaler.data_min_[0]     # m³/s
print(f"ΔQ (range of discharge) = {delta_Q:.3f}  m³/s")

# ---------- 3. 换算到 m³/s ----------
shap_m3s = shap_df * delta_Q

# ---------- 4. 保存 TXT ----------
shap_m3s.to_csv(TXT_OUT, sep="\t", float_format="%.4f",
                na_rep="")                 # 保留 t0 的空白
print("✔ 已写出物理单位矩阵：", TXT_OUT)

# ---------- 5. 画热力图 ----------
plt.figure(figsize=(1.2*shap_m3s.shape[1], 0.7*shap_m3s.shape[0]+1))
sns.heatmap(shap_m3s, annot=True, fmt=".2f", cmap="Reds",
            cbar_kws={"label": "Mean |SHAP|  (m³/s)"})
plt.title("Mean |SHAP|   (larger ⇒ stronger impact)     [in m³/s]")
plt.tight_layout()
plt.savefig(PNG_OUT, dpi=300)
plt.close()
print("✔ 热力图已保存：", PNG_OUT)
