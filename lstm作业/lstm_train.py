# -*- coding: utf-8 -*-
"""
训练 LSTM+Exog 并保存 pdparams & meta.json
------------------------------------------------
* 数据由 00_预处理脚本 生成：
      ├── train_look24.npz   X_hist:(N,24,7)   X_exog:(N,6)   y:(N,)
      ├── test_look24.npz
      └── meta.json          记录 look_back / input_dim / exog_dim ...
"""

import json, os, pickle, sys
from pathlib import Path

import numpy as np
import paddle
import paddle.nn as nn
from paddle.io import Dataset, DataLoader
from tqdm import tqdm

# ============ 1.  路径 ============
BASE_DIR = Path(r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903")
DATA_DIR = BASE_DIR / "预处理输出"
TRAIN_NPZ = DATA_DIR / "train_look24.npz"
TEST_NPZ  = DATA_DIR / "test_look24.npz"
META_JSON = DATA_DIR / "meta.json"
# ==================================

with open(META_JSON, encoding="utf-8") as f:
    cfg = json.load(f)

LOOK_BACK   = cfg["look_back"]          # 24
INPUT_DIM   = cfg["input_dim"]          # 7
EXOG_DIM    = cfg["exog_dim"]           # 6  <-- 已改
HIDDEN_SIZE = cfg["hidden_size"]        # 132
NUM_LAYERS  = cfg["num_layers"]         # 2

BATCH_SIZE  = 64
NUM_EPOCHS  = 40
LR          = 5e-4
PATIENCE    = 8       # 早停 (patience=0 关闭)

# -------- 2. 数据集封装 ----------
class FloodDataset(Dataset):
    def __init__(self, npz_path: Path):
        d = np.load(npz_path)
        self.Xh = d["X_hist"].astype("float32")   # (N, 24, 7)
        self.Xe = d["X_exog"].astype("float32")   # (N, 6)
        self.y  = d["y"     ].astype("float32")   # (N,)
    def __len__(self): return len(self.y)
    def __getitem__(self, idx):
        return self.Xh[idx], self.Xe[idx], self.y[idx]

train_loader = DataLoader(FloodDataset(TRAIN_NPZ), batch_size=BATCH_SIZE,
                          shuffle=True, drop_last=True, num_workers=0)
test_loader  = DataLoader(FloodDataset(TEST_NPZ),  batch_size=BATCH_SIZE,
                          shuffle=False,           num_workers=0)

# -------- 3. 模型 ----------
class LSTMWithExog(nn.Layer):
    """
    输入:
        hist : (B, LOOK_BACK, INPUT_DIM=7)
        exog : (B, EXOG_DIM=6)  ← t0 时刻 5 站雨量 + 蒸发
    输出:
        y_hat: (B,)
    """
    def __init__(self, input_dim, hidden_size, num_layers, exog_dim):
        super().__init__()
        self.lstm = nn.LSTM(input_dim, hidden_size,
                            num_layers=num_layers,
                            direction="forward")
        self.fc   = nn.Sequential(
            nn.Linear(hidden_size + exog_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )

    def forward(self, hist, exog):
        # hist  (B, T, F) -> (T, B, F)
        hist = paddle.transpose(hist, [1, 0, 2])
        out, _ = self.lstm(hist)
        last   = out[-1]                       # (B, hidden)
        x      = paddle.concat([last, exog], axis=1)
        return self.fc(x).squeeze(1)           # (B,)

# -------- 4. 训练准备 ----------
device = "gpu" if paddle.is_compiled_with_cuda() else "cpu"
paddle.set_device(device)

model = LSTMWithExog(INPUT_DIM, HIDDEN_SIZE, NUM_LAYERS, EXOG_DIM)
criterion = nn.MSELoss()
optimizer = paddle.optimizer.Adam(learning_rate=LR, parameters=model.parameters())

best_val = np.inf
wait = 0

# -------- 5. 训练循环 ----------
for epoch in range(1, NUM_EPOCHS + 1):
    # ---- Train ----
    model.train()
    pbar = tqdm(train_loader, desc=f"Epoch {epoch}/{NUM_EPOCHS}", ncols=110)
    for Xh, Xe, y in pbar:
        loss = criterion(model(Xh, Xe), y)
        loss.backward()
        optimizer.step(); optimizer.clear_gradients()
        pbar.set_postfix(train_loss=f"{loss.numpy().item():.6f}")

    # ---- Validate ----
    model.eval(); val_losses=[]
    with paddle.no_grad():
        for Xh, Xe, y in test_loader:
            val_losses.append(criterion(model(Xh, Xe), y).numpy().item())
    val_loss = float(np.mean(val_losses))
    print(f"Epoch {epoch:2d}  ValLoss={val_loss:.6f}")

    # ---- Early-Stopping ----
    if val_loss < best_val - 1e-6:
        best_val = val_loss; wait = 0
        paddle.save(model.state_dict(),
                    str(DATA_DIR / "lstm_with_exog.best.pdparams"))
    else:
        wait += 1
        if PATIENCE and wait >= PATIENCE:
            print("♦ 早停触发，停止训练")
            break

# -------- 6. 保存最终权重 ----------
final_path = DATA_DIR / "lstm_with_exog.pdparams"
paddle.save(model.state_dict(), str(final_path))
print("✔ 最终参数已保存：", final_path)
print("✔ 最优参数已保存：", DATA_DIR / "lstm_with_exog.best.pdparams")
