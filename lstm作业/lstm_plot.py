# -*- coding: utf-8 -*-
"""
逐场次反归一 → 预测 → 画对比图   (t0 外生量 = 5 站降雨 + Evap)
并生成汇总表：RMSE & NSE
"""

import json, pickle, math
from pathlib import Path

import numpy as np
import pandas as pd
import paddle
import paddle.nn as nn
import matplotlib.pyplot as plt
from tqdm import tqdm
from sklearn.metrics import mean_squared_error

# ---------- 常量 ----------
TARGET_IDX = 0           # 流量列在特征矩阵中的索引

# ---------- 路径 ----------
BASE_DIR  = Path(r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903")
XLS_PATH  = BASE_DIR / "安徽中小流域资料整理 - 给孟涵 - 20230903" / "月潭次模率定.xls"
DATA_DIR  = BASE_DIR / "预处理输出"
PLOTS_DIR = BASE_DIR / "plots"
PLOTS_DIR.mkdir(exist_ok=True)

# ---------- 读 meta & scaler ----------
with open(DATA_DIR / "meta.json", encoding="utf-8") as f:
    cfg = json.load(f)

LOOK_BACK = cfg["look_back"]      # 24
INPUT_DIM = cfg["input_dim"]      # 7
EXOG_DIM  = cfg["exog_dim"]       # 6（P1-P5 + Evap）

with open(DATA_DIR / "minmax_scaler.pkl", "rb") as f:
    scaler = pickle.load(f)

# ---------- 模型 ----------
class LSTMWithExog(nn.Layer):
    def __init__(self, input_dim, hidden_size, num_layers, exog_dim=EXOG_DIM):
        super().__init__()
        self.lstm = nn.LSTM(input_dim, hidden_size, num_layers, direction="forward")
        self.fc   = nn.Sequential(
            nn.Linear(hidden_size + exog_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
    def forward(self, hist, exog):
        hist = paddle.transpose(hist, [1, 0, 2])        # (seq,batch,feat)
        out, _ = self.lstm(hist)
        last   = out[-1]
        x      = paddle.concat([last, exog], axis=1)     # 加上 t0 外生量
        return self.fc(x).squeeze(1)

# ---------- 权重 ----------
state_dict = paddle.load(str(DATA_DIR / "lstm_with_exog.pdparams"))
hidden_sz  = state_dict["lstm.weight_hh_l0"].shape[1]   # 解析 hidden
model = LSTMWithExog(INPUT_DIM, hidden_sz, cfg["num_layers"])
model.set_state_dict(state_dict)
model.eval()
print("✔ 已加载权重：", DATA_DIR / "lstm_with_exog.pdparams")

# ---------- 读取原始 excel ----------
def int2dt(series, unit="H"):
    fmt = "%Y%m%d%H" if unit.upper()=="H" else "%Y%m%d"
    return pd.to_datetime(series.astype(str).str.zfill(len(fmt)-2), format=fmt)

summary_df = pd.read_excel(XLS_PATH, sheet_name="summary", engine="xlrd")
data_df    = pd.read_excel(XLS_PATH, sheet_name="data",    engine="xlrd")
evap_df    = pd.read_excel(XLS_PATH, sheet_name="evapd",   engine="xlrd")

data_df["ts"]   = int2dt(data_df.iloc[:,0],"H"); data_df.set_index("ts", inplace=True)
evap_df["date"] = int2dt(evap_df.iloc[:,0],"D"); evap_df.set_index("date", inplace=True)
evap_df.drop(columns=evap_df.columns[0], inplace=True)

evap_h = evap_df.resample("h").ffill().rename(columns={evap_df.columns[0]:"evap"})
flow_col  = data_df.columns[1]
rain_cols = data_df.columns[2:]
feature_df = data_df[[flow_col, *rain_cols]].join(evap_h["evap"], how="left")

def to_tensor(arr):
    return paddle.to_tensor(arr, dtype="float32")

# ---------- 逐场次预测 ----------
metrics = []        # 收集 [eid, RMSE, NSE]

for _, row in tqdm(summary_df.iterrows(), total=len(summary_df), desc="Predict"):
    eid = int(row.iloc[0])
    st  = pd.to_datetime(str(int(row.iloc[1])), format="%Y%m%d%H")
    ed  = pd.to_datetime(str(int(row.iloc[3])), format="%Y%m%d%H")
    seg = feature_df.loc[st:ed]
    if len(seg) <= LOOK_BACK:
        print(f"[跳过] Event {eid} 长度<{LOOK_BACK}")
        continue

    seg_filled = seg.interpolate("linear").ffill().bfill()
    seg_scaled = scaler.transform(seg_filled.values)

    y_obs, y_sim = [], []
    for i in range(len(seg_scaled) - LOOK_BACK):
        hist = seg_scaled[i:i+LOOK_BACK, :]          # (24,7)
        exog = seg_scaled[i+LOOK_BACK, 1:]           # P1-P5 + Evap  (6,)

        y_hat_norm = model(to_tensor(hist[None,...]),
                           to_tensor(exog[None,...])).numpy()[0]

        dummy = np.zeros((1, seg_scaled.shape[1]))
        dummy[0, TARGET_IDX] = y_hat_norm
        y_hat_real = scaler.inverse_transform(dummy)[0, TARGET_IDX]

        y_sim.append(y_hat_real)
        y_obs.append(seg.iloc[i+LOOK_BACK, TARGET_IDX])

    # ---------- 计算指标 ----------
    rmse = math.sqrt(mean_squared_error(y_obs, y_sim))
    obs_arr = np.asarray(y_obs)
    sim_arr = np.asarray(y_sim)
    nse  = 1.0 - np.sum((sim_arr - obs_arr)**2) / np.sum((obs_arr - obs_arr.mean())**2)

    metrics.append([eid, rmse, nse])

    # ---------- 画图 ----------
    plt.figure(figsize=(14,4))
    plt.plot(y_obs, label="Observed")
    plt.plot(y_sim, label="Simulated")
    plt.title(f"Event {eid} │ RMSE={rmse:.3f} │ NSE={nse:.3f}")
    plt.xlabel("Time step (h)")
    plt.ylabel("Discharge (m³/s)")
    plt.legend()
    png_path = PLOTS_DIR / f"event_{eid}.png"
    plt.tight_layout(); plt.savefig(png_path, dpi=120); plt.close()
    print(f"Event {eid:>3d}  RMSE={rmse:.3f}  NSE={nse:.3f}  → {png_path}")

# ---------- 汇总表 ----------
metric_df = pd.DataFrame(metrics, columns=["EventID", "RMSE_m3s", "NSE"])
metric_df.sort_values("EventID", inplace=True)

TSV_PATH = DATA_DIR / "event_metrics.tsv"
metric_df.to_csv(TSV_PATH, sep="\t", float_format="%.3f", index=False)

print("\n=====  Evaluation summary  =====")
print(metric_df.to_string(index=False, formatters={"RMSE_m3s":"{:.3f}".format,
                                                   "NSE":"{:.3f}".format}))
print("\n✔ 指标表已保存：", TSV_PATH)
print("✔ 全部 PNG 保存于：", PLOTS_DIR)
