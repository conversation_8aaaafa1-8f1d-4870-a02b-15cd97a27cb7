from paddlets import TSDataset
from paddlets.transform import OneHot

# 1 prepare the data
data = TSDataset.load_from_csv("/path/to/data.csv")

# 2 init the onehot encoder
encoder = OneHot(cols=["Gender"]) #使用OneHot编码对"Gender"列进行编码

# 3 fit the encoder
encoder.fit(dataset=data)#使用fit方法对数据进行编码

# 4 do transformation with the learnt encoder
transformed_data = encoder.transform(data, inplace=False)#使用transform方法对数据进行转换