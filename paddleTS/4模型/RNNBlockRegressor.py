from paddlets import TSDataset
from paddlets.models.model_loader import load
from paddlets.models.forecasting import RNNBlockRegressor
'''paddlets.models.forecasting中现成的11个模型
from paddlets.models.forecasting.dl.mlp import MLPRegressor
from paddlets.models.forecasting.dl.rnn import RNNBlockRegressor
from paddlets.models.forecasting.dl.lstnet import LSTNetRegressor
from paddlets.models.forecasting.dl.nbeats import NBEATSModel
from paddlets.models.forecasting.dl.tcn import TCNRegressor
from paddlets.models.forecasting.dl.nhits import NHiTSModel
from paddlets.models.forecasting.dl.transformer import TransformerModel
from paddlets.models.forecasting.dl.informer import InformerModel
from paddlets.models.forecasting.dl.deepar import DeepARModel
from paddlets.models.forecasting.dl.tft import TFTModel
from paddlets.models.forecasting.dl.scinet import SCINetModel
'''
# 1 prepare the data
# 1 准备数据
data = TSDataset.load_from_csv("/path/to/data.csv")

# 2 data preprocessing and feature engineering
# 2 数据预处理和特征工程
# NB:
# Consider simplifying the example, all these important processes are skipped here.
# 考虑简化的示例，所有的引用都被跳过了
# Please refer to the following documentation to get more details if needed:
# 请参考以下文档以获取更多详细信息：
# https://paddlets.readthedocs.io/en/latest/source/modules/transform/overview.html
# transform 会按流水线方式（pipeline）自动应用到 TSDataset 中，保证模型输入的时序矩阵在数值尺度和缺失情况上都满足训练要求。

# 3 init the model instance.
# 3 初始化模型实例

 # 用RNNBlockRegressor创建模型赋值给model，一种基于循环神经网络（RNN/LSTM/GRU）模块的回归型预测模型
# 关键参数：
# in_chunk_len=96：表示输入序列的“历史窗口长度”为 96（时间步）。也就是说，模型每次训练时会取连续 96 个时刻的样本来预测未来。
# out_chunk_len=96：表示预测的输出窗口长度为 96（时间步）。因此，每次模型做一次前向传播，会尝试预测下一个 96 时刻的目标值。
# 如果你的数据频率是小时级（freq="H"），那么这相当于“用过去 96 小时来预测未来 96 小时”；如果是分钟级、天级亦同理。
model = RNNBlockRegressor(in_chunk_len=96, out_chunk_len=96)

# 4 fit
# fit 方法会将传入的 TSDataset（此处即 data）拆分成若干个“样本——标签”对：
# 样本：长度为 in_chunk_len 的时序片段；
# 标签：紧接在样本后面的长度为 out_chunk_len 的连续时刻值。
# 在内部，PaddleTS 会根据 data 中的时间索引、目标列以及协变量列（如果有配置）自动组建训练集。
# 训练过程包括以下几步：
# Batch 采样：如果数据集很大，会按批次（batch）随机或顺序采样若干段时序；
# 前向传播：将长度为 in_chunk_len 的输入送入 RNN 模块，提取隐藏向量后，通过后续全连接层或时序解码结构输出长度为 out_chunk_len 的预测；
# 计算损失：典型地使用均方误差（MSE）或平均绝对误差（MAE）作为回归损失；
# 反向传播与优化：利用优化器（默认 Adam 或者由用户指定）更新 RNN 和全连接层的权重；
# 迭代：重复上述步骤若干个 epoch，直到满足早停、达到最大迭代轮次或者损失趋于稳定。
# 训练结束后，model 对象内部会保存好各层的参数，以及与预处理流水线（transform）相关的统计量（如均值、方差等）。
model.fit(train_tsdataset=data)

# 5 predict
# predict 默认做“一次性”的批量预测：
# 它会先把 data（TSDataset）切片成多个「长度为 in_chunk_len 的上下文窗口」；
# 对每个窗口调用前向推理，直接得到对应的「长度为 out_chunk_len」的预测；
# 最终把所有窗口拼接起来，输出一个新的 TSDataset 作为 predicted_dataset。
# 其中：
# 输入部分是“样本历史窗口”；
# 输出则对应这些样本之后的“未来 out_chunk_len 步”预测值；
# 通常在「训练集内」或「验证集内」做一次性推断时使用 predict，得到预测时间段与原标签时间段对齐的结果。
predicted_dataset = model.predict(data)

# 6 recursive predict
# “递归预测”表示从最新的已知时序点PaddleBaseModel
# 7 save the model
model.save("/path/to/save/modelname")

# 8 load the model
loaded_model = load("/path/to/save/modelname")