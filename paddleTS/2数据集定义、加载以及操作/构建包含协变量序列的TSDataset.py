import pandas as pd
import numpy as np
from matplotlib import pyplot as plt
from paddlets import TSDataset

x = np.linspace(-np.pi, np.pi, 200)  #调用numpy在-π,π 上等间隔地生成 200 个数
sinx = np.sin(x) * 4 + np.random.randn(200) #在x 数组上构造一个“有噪声”的正弦波信号
# 构造一个pandas.DataFrame，DataFrame是pandas中的二维表格，作用是用来存储和处理一组相关的数据
# 注意，time_col 列需要是 datetime 类型
df = pd.DataFrame(
    {
        'time_col': pd.date_range('2022-01-01', periods=200, freq='1h'),
        'value': sinx,
        'known_cov_1': sinx + 4,
        'known_cov_2': sinx + 5,
        'observed_cov': sinx + 8,
        'static_cov': [1 for i in range(200)],
    }
)

# 转化为TSDataset
#load_from_dataframe的第一个参数是 DataFrame 或者 CSV 文件路径
# time_col 参数是 DataFrame 中包含时间信息的列名
# target_cols 参数是 DataFrame 中包含需要预测的列名
#known_cov_cols 参数是 DataFrame 中包含已知协变量的列名
# observed_cov_cols 参数是 DataFrame 中包含观测协变量的列名
# static_cov_cols 参数是 DataFrame 中包含静态协变量的列名
# freq 参数是数据的时间频率
target_cov_dataset = TSDataset.load_from_dataframe(
    df,
    time_col='time_col',
    target_cols='value',
    known_cov_cols=['known_cov_1', 'known_cov_2'],
    observed_cov_cols='observed_cov',
    static_cov_cols='static_cov',
    freq='1h'
)

# 画图，返回 Axes 对象
ax = target_cov_dataset.plot(
    ['value', 'known_cov_1', 'known_cov_2', 'observed_cov']
)

# 阻塞式显示图窗，直到你手动关闭
plt.show()