
"""
PaddleTS 基础数据存放类 TSDataset 及其构造方法

本模块演示如何通过 pandas 中的 DataFrame 二维表格来构造 TSDataset 实例，
并介绍 TSDataset 中的主要属性：
- time_col：时间列
- target_cols：目标变量列
- known_cov_cols：可预知协变量列
- observed_cov_cols：观测协变量列
- static_cov_cols：静态协变量列
- freq：时间频率
"""
import pandas as pd
import numpy as np
from matplotlib import pyplot as plt
from paddlets import TSDataset

x = np.linspace(-np.pi, np.pi, 200)  #调用numpy在-π,π 上等间隔地生成 200 个数
sinx = np.sin(x) * 4 + np.random.randn(200) #在x 数组上构造一个“有噪声”的正弦波信号

# 构造一个pandas.DataFrame，DataFrame是pandas中的二维表格，作用是用来存储和处理一组相关的数据
df = pd.DataFrame(
    {
        #调用pandas的date_range在'2022-01-01'之后生成200个时间戳，间隔为1小时
        'time_col': pd.date_range('2022-01-01', periods=200, freq='1h'),
        'value': sinx
    }
)

# 创建一个TSDataset实例，TSDataset是Paddlets中用来表示时间序列数据的类
# 其中，time_col是时间列，target_cols是要预测的列
#这里的设置方式是从df中或者从csv中读取数据，然后按'1h'的频率分成一个小时为单位的数据
target_dataset = TSDataset.load_from_dataframe(
    df,  #Also can be path to the CSV file
    time_col='time_col',
    target_cols='value',
    freq='1h'
)

# 画图
ax = target_dataset.plot()

# 阻塞式显示，直到你手动关掉图窗
plt.show()

