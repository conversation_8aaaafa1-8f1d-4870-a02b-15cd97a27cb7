import pandas as pd
import numpy as np
from paddlets import TSDataset

#Build DataFrame with group_id
sample = pd.DataFrame(np.random.randn(200, 3), columns=['a', 'c', 'd'])
sample['id'] = pd.Series([0]*80 + [1]*120, name='id')

#Load TSDatasets by group_id
tsdatasets = TSDataset.load_from_dataframe(
    df=sample,
    group_id='id',
    target_cols='a',
    observed_cov_cols=['c', 'd'],
    #static_cov_cols='id'
)

print(f" The type of tsdatasets is {type(tsdatasets)},\n \
and the length of tsdatasets is {len(tsdatasets)},\n \
the length of first tsdataset target is {len(tsdatasets[0].target)},\n \
the length of second tsdataset target is {len(tsdatasets[1].target)}")
# The type of tsdatasets is <class 'list'>,
# and the length of tsdatasets is 2,
# the length of first tsdataset target is 80,
# the length of second tsdataset target is 120