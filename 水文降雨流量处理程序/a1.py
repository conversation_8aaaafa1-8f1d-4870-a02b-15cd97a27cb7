import os
import configparser


def modify_pip_config():
    # 确定配置文件路径
    if os.name == 'nt':  # Windows
        config_dir = os.path.join(os.environ['APPDATA'], 'pip')
        config_path = os.path.join(config_dir, 'pip.ini')
    else:  # Linux/macOS
        config_dir = os.path.expanduser('~/.config/pip')
        config_path = os.path.join(config_dir, 'pip.conf')

    # 创建配置目录（如果不存在）
    os.makedirs(config_dir, exist_ok=True)

    # 读取现有配置（如果存在）
    config = configparser.ConfigParser()
    if os.path.exists(config_path):
        config.read(config_path)

    # 设置全局镜像源为官方源
    if not config.has_section('global'):
        config.add_section('global')
    config.set('global', 'index-url', 'https://pypi.org/simple')

    # 保存配置
    with open(config_path, 'w') as f:
        config.write(f)

    print(f"已修改pip配置文件: {config_path}")
    print("现在使用的是官方镜像源: https://pypi.org/simple")


if __name__ == "__main__":
    modify_pip_config()