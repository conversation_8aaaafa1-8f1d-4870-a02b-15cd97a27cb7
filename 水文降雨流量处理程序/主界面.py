import sys
import os

from PyQt5.QtWidgets import (
    QApplication,
    QMainWindow,
    QWidget,
    QHBoxLayout,
    QVBoxLayout,
    QPushButton,
    QFileDialog,
    QMessageBox,
    QFrame,
    QDesktopWidget,
    QLabel,
)
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtCore import Qt


# 现代深色主题配色方案
PRIMARY_COLOR = "#2563EB"      # 现代蓝色主色调
SECONDARY_COLOR = "#3B82F6"    # 较亮的蓝色辅助色
ACCENT_COLOR = "#10B981"       # 绿色强调色
SUCCESS_COLOR = "#059669"      # 成功状态绿色
WARNING_COLOR = "#F59E0B"      # 警告橙色
DANGER_COLOR = "#EF4444"       # 危险红色

BACKGROUND_COLOR = "#0F172A"   # 深蓝灰色背景
SURFACE_COLOR = "#1E293B"      # 表面颜色（面板背景）
CARD_COLOR = "#334155"         # 卡片颜色
BORDER_COLOR = "#475569"       # 边框颜色

TEXT_PRIMARY = "#F8FAFC"       # 主要文本颜色
TEXT_SECONDARY = "#CBD5E1"     # 次要文本颜色
TEXT_MUTED = "#94A3B8"         # 静音文本颜色


class MainWindow(QMainWindow):
    """水文降雨流量插值处理软件（GUI框架）。"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("水文降雨流量插值处理程序")

        # 获取屏幕尺寸并设置窗口为屏幕的80%
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        self.screen_width = screen_rect.width()
        self.screen_height = screen_rect.height()

        # 窗口大小为屏幕的80%
        window_width = int(self.screen_width * 0.8)
        window_height = int(self.screen_height * 0.8)
        self.resize(window_width, window_height)

        # 最小尺寸为屏幕的50%
        min_width = int(self.screen_width * 0.5)
        min_height = int(self.screen_height * 0.5)
        self.setMinimumSize(min_width, min_height)

        # 居中显示
        self.move(
            (self.screen_width - window_width) // 2,
            (self.screen_height - window_height) // 2
        )

        self._init_ui()

    # ------------------------------------------------------------------
    # 用户界面构建
    # ------------------------------------------------------------------
    def _init_ui(self):
        # 根据屏幕尺寸计算基础字体大小
        base_font_size = max(12, int(self.screen_width * 0.012))  # 屏幕宽度的1.2%
        large_font_size = int(base_font_size * 1.4)  # 大字体
        button_font_size = int(base_font_size * 1.6)  # 按钮字体

        # 计算相对尺寸
        border_radius = max(8, int(self.screen_width * 0.008))  # 圆角
        padding = max(12, int(self.screen_width * 0.012))  # 内边距
        margin = max(8, int(self.screen_width * 0.008))  # 外边距
        button_height = max(40, int(self.screen_height * 0.06))  # 按钮高度为屏幕高度的6%

        # 通过样式表设置全局应用程序调色板
        self.setStyleSheet(
            f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {BACKGROUND_COLOR}, stop:1 #1a202c);
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
                font-size: {base_font_size}px;
            }}

            QLabel {{
                color: {TEXT_PRIMARY};
                font-size: {large_font_size}px;
                font-weight: 600;
            }}

            QFrame#panel {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {SURFACE_COLOR}, stop:1 {CARD_COLOR});
                border: 1px solid {BORDER_COLOR};
                border-radius: {border_radius}px;
                padding: {padding}px;
                margin: {margin}px;
            }}

            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {SURFACE_COLOR}, stop:1 {CARD_COLOR});
                border: 1px solid {BORDER_COLOR};
                border-radius: {border_radius}px;
                padding: {padding}px {padding * 1.2}px;
                color: {TEXT_PRIMARY};
                font-size: {button_font_size}px;
                font-weight: 600;
                text-align: center;
                min-height: {button_height}px;
            }}

            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {PRIMARY_COLOR}, stop:1 {SECONDARY_COLOR});
                border: 1px solid {PRIMARY_COLOR};
                color: {TEXT_PRIMARY};
            }}

            QPushButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {ACCENT_COLOR}, stop:1 {SUCCESS_COLOR});
                border: 1px solid {ACCENT_COLOR};
                color: {TEXT_PRIMARY};
                font-weight: 600;
            }}

            QPushButton:pressed {{
                background: {CARD_COLOR};
                border: 1px solid {BORDER_COLOR};
            }}

            /* 为数据处理按钮添加特殊样式 */
            QPushButton[objectName="data_btn"] {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {SURFACE_COLOR}, stop:1 {CARD_COLOR});
                border: 1px solid {BORDER_COLOR};
                border-radius: {border_radius + 2}px;
                padding: {padding * 1.5}px {padding * 1.8}px;
                margin: {margin}px;
                font-size: {button_font_size * 1.2}px;
                font-weight: 600;
                min-height: {button_height * 1.2}px;
            }}

            QPushButton[objectName="data_btn"]:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {SECONDARY_COLOR}, stop:1 {PRIMARY_COLOR});
                border: 1px solid {SECONDARY_COLOR};
                font-size: {button_font_size * 1.2}px;
            }}
            """
        )

        central = QWidget()
        self.setCentralWidget(central)
        main_layout = QHBoxLayout(central)
        main_layout.setContentsMargins(margin, margin, margin, margin)  # 使用相对外边距
        main_layout.setSpacing(padding)  # 面板间距

        # 左侧 — 文件面板 ------------------------------------------------
        self.file_panel = QFrame(objectName="panel")
        file_layout = QVBoxLayout(self.file_panel)
        file_layout.setContentsMargins(padding * 1.5, padding * 1.5, padding * 1.5, padding * 1.5)  # 面板内边距
        file_layout.setSpacing(padding)  # 按钮间距

        self.flow_btn = self._make_toggle_button("流量数据库连接", button_height)
        self.rain_btn = self._make_toggle_button("降雨数据库连接", button_height)
        self.output_btn = self._make_toggle_button("输出数据库连接", button_height)

        for btn in (self.flow_btn, self.rain_btn, self.output_btn):
            btn.clicked.connect(lambda checked, b=btn: self._select_db(b))
            file_layout.addWidget(btn)

        file_layout.addStretch(1)

        # 右侧 — 数据处理面板 -----------------------------------
        self.data_panel = QFrame(objectName="panel")
        dp_layout = QVBoxLayout(self.data_panel)
        dp_layout.setContentsMargins(padding * 1.5, padding * 1.5, padding * 1.5, padding * 1.5)  # 面板内边距
        dp_layout.setSpacing(padding)  # 按钮间距

        dp_buttons = [
            "初始数据展示",
            "洪水场次确定",
            "降雨数据插值处理",
            "流量数据插值处理",
            "插值后数据展示",
        ]
        for name in dp_buttons:
            btn = QPushButton(name)
            btn.setObjectName("data_btn")  # 设置对象名称以应用特殊样式
            btn.setMinimumHeight(int(button_height * 1.2))  # 使用动态计算的按钮高度
            btn.clicked.connect(self._placeholder)
            dp_layout.addWidget(btn)
        dp_layout.addStretch(1)

        # 组装面板 --------------------------------------------------
        main_layout.addWidget(self.file_panel, 1)
        main_layout.addWidget(self.data_panel, 2)

    # ------------------------------------------------------------------
    # 辅助方法
    # ------------------------------------------------------------------
    def _make_toggle_button(self, text: str, height: int) -> QPushButton:
        btn = QPushButton(text)
        btn.setMinimumHeight(height)  # 使用动态计算的按钮高度
        btn.setCheckable(True)
        return btn

    def _select_db(self, btn: QPushButton):
        """打开文件对话框；如果选择了路径则标记按钮为选中状态。"""
        path, _ = QFileDialog.getOpenFileName(
            self,
            "选择数据库文件",
            os.getcwd(),
            "Database Files (*.db *.sqlite *.mdb *.accdb);;All Files (*.*)",
        )
        if path:
            btn.setChecked(True)
            btn.setToolTip(path)  # 悬停显示路径
        else:
            btn.setChecked(False)
            btn.setToolTip("")

    def _placeholder(self):
        """为未实现的功能显示占位符对话框。"""
        QMessageBox.information(self, "功能暂未实现", "此功能正在开发中，敬请期待！")


# ----------------------------------------------------------------------
# 应用程序入口点
# ----------------------------------------------------------------------
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon())  # 占位符：如果有图标文件可在此添加路径
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
