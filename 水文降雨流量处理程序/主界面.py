import sys
import os
import pyodbc
import pandas as pd
import numpy as np
from pathlib import Path
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, QPushButton,
    QFileDialog, QMessageBox, QFrame, QDesktopWidget, QLabel, QDialog,
    QTableWidget, QTableWidgetItem, QHeaderView, QProgressDialog, QVBoxLayout
)
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtCore import Qt, QThread, pyqtSignal

# 现代深色主题配色方案
COLORS = {
    'primary': "#2563EB", 'secondary': "#3B82F6", 'accent': "#10B981", 'success': "#059669",
    'warning': "#F59E0B", 'danger': "#EF4444", 'background': "#0F172A", 'surface': "#1E293B",
    'card': "#334155", 'border': "#475569", 'text_primary': "#F8FAFC",
    'text_secondary': "#CBD5E1", 'text_muted': "#94A3B8"
}


class RainDataReader(QThread):
    """雨量数据读取线程"""
    progress_updated = pyqtSignal(str)  # 进度更新信号
    data_loaded = pyqtSignal(dict)      # 数据加载完成信号
    error_occurred = pyqtSignal(str)    # 错误信号

    def __init__(self, db_path):
        super().__init__()
        self.db_path = db_path

    def run(self):
        try:
            print("RainDataReader线程开始运行")
            self.progress_updated.emit("正在连接雨量数据库...")

            # 构建数据库连接字符串
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                fr"DBQ={self.db_path}"
            )
            print(f"连接字符串: {conn_str}")

            with pyodbc.connect(conn_str) as conn:
                self.progress_updated.emit("数据库连接成功，开始读取数据...")

                # 读取雨量站点信息
                self.progress_updated.emit("读取雨量站点信息...")
                df_station = pd.read_sql("SELECT * FROM [P-Station]", conn)

                # 读取降雨观测数据
                self.progress_updated.emit("读取降雨观测数据...")
                df_data = pd.read_sql("SELECT * FROM [表]", conn)

            self.progress_updated.emit("数据读取完成，正在处理...")

            # 数据格式转换
            station_arr = df_station.values
            data_arr = df_data.values

            # 按站点分组处理
            self.progress_updated.emit("正在按站名对数据进行分组...")
            key_col = 2  # 站名列
            keys = np.unique(data_arr[:, key_col])

            # 创建站点信息字典
            stations_info = {}
            for station_key in keys:
                station_records = data_arr[data_arr[:, key_col] == station_key]
                record_count = station_records.shape[0]

                # 查找站点基础信息
                station_info_row = None
                for row in station_arr:
                    if str(row[2]) == str(station_key):
                        station_info_row = row
                        break

                stations_info[station_key] = {
                    'name': str(station_key),
                    'record_count': record_count,
                    'has_station_info': station_info_row is not None
                }

            self.progress_updated.emit("数据处理完成！")
            print(f"数据处理完成，共{len(stations_info)}个站点")
            print("准备发射data_loaded信号...")
            self.data_loaded.emit(stations_info)
            print("data_loaded信号已发射")

        except Exception as e:
            print(f"RainDataReader发生错误: {e}")
            self.error_occurred.emit(f"读取数据时发生错误：{str(e)}")


class StationInfoDialog(QDialog):
    """雨量站信息展示对话框"""

    def __init__(self, stations_info, parent=None):
        super().__init__(parent)
        self.stations_info = stations_info
        self.setWindowTitle(f"雨量站信息 - 共{len(stations_info)}个站点")
        self.setModal(False)  # 改为非模态对话框

        # 获取屏幕尺寸并设置响应式窗口
        if parent:
            screen_width = parent.screen_width
            screen_height = parent.screen_height
        else:
            screen = QDesktopWidget().screenGeometry()
            screen_width, screen_height = screen.width(), screen.height()

        # 计算响应式尺寸
        self.sizes = {
            'base_font': max(11, int(screen_width * 0.0095)),    # 基础字体
            'table_font': max(13, int(screen_width * 0.009)),    # 表格字体（稍微增大）
            'header_font': max(15, int(screen_width * 0.0105)), # 表头字体（稍微增大）
            'button_font': max(13, int(screen_width * 0.0095)), # 按钮字体（稍微增大）
            'padding': max(6, int(screen_width * 0.005)),        # 内边距（减小）
            'button_height': max(40, int(screen_height * 0.045)), # 按钮高度（稍微增大）
            'row_height': max(28, int(screen_height * 0.032)),   # 表格行高（稍微减小）
        }

        # 设置窗口尺寸（屏幕的60%）
        dialog_width = int(screen_width * 0.6)
        dialog_height = int(screen_height * 0.7)
        self.resize(dialog_width, dialog_height)
        self.setMinimumSize(int(screen_width * 0.4), int(screen_height * 0.5))

        # 居中显示
        self.move((screen_width - dialog_width) // 2, (screen_height - dialog_height) // 2)

        # 设置窗口标志，确保窗口能正常显示
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinMaxButtonsHint)

        print(f"创建雨量站信息对话框，包含 {len(stations_info)} 个站点")

        try:
            self._init_ui()
            print("雨量站信息对话框初始化完成")
        except Exception as e:
            print(f"初始化对话框时发生错误: {e}")

    def closeEvent(self, event):
        """重写关闭事件"""
        print("雨量站信息对话框被关闭")
        event.accept()

    def showEvent(self, event):
        """重写显示事件"""
        print("雨量站信息对话框显示")
        super().showEvent(event)

    def _init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(self.sizes['padding'] * 2, self.sizes['padding'] * 2,
                                 self.sizes['padding'] * 2, self.sizes['padding'] * 2)
        layout.setSpacing(self.sizes['padding'])

        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["站点名称", "记录数量", "基础信息"])

        # 设置表格样式
        c = COLORS
        self.table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {c['surface']};
                color: {c['text_primary']};
                border: 1px solid {c['border']};
                border-radius: {self.sizes['padding']}px;
                font-size: {self.sizes['table_font']}px;
                gridline-color: {c['border']};
                selection-background-color: {c['primary']};
            }}
            QTableWidget::item {{
                padding: {self.sizes['padding']}px {self.sizes['padding'] * 1.5}px;
                border-bottom: 1px solid {c['border']};
                min-height: {self.sizes['row_height']}px;
            }}
            QTableWidget::item:selected {{
                background-color: {c['primary']};
                color: {c['text_primary']};
            }}
            QTableWidget::item:hover {{
                background-color: rgba(37, 99, 235, 0.1);
            }}
            QHeaderView::section {{
                background-color: {c['card']};
                color: {c['text_primary']};
                padding: {self.sizes['padding'] * 1.2}px;
                border: none;
                font-weight: 600;
                font-size: {self.sizes['header_font']}px;
                min-height: {self.sizes['row_height'] * 1.2}px;
            }}
            QHeaderView::section:hover {{
                background-color: {c['border']};
            }}
        """)

        # 填充数据
        self._populate_table()

        # 设置表格属性
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)

        layout.addWidget(self.table)

        # 添加关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.setMinimumHeight(self.sizes['button_height'])
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {c['primary']}, stop:1 {c['secondary']});
                color: {c['text_primary']};
                border: 1px solid {c['primary']};
                border-radius: {self.sizes['padding']}px;
                padding: {self.sizes['padding']}px {self.sizes['padding'] * 2}px;
                font-size: {self.sizes['button_font']}px;
                font-weight: 600;
                margin: {self.sizes['padding'] // 2}px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {c['secondary']}, stop:1 {c['primary']});
                border: 1px solid {c['secondary']};
            }}
            QPushButton:pressed {{
                background: {c['card']};
                border: 1px solid {c['border']};
            }}
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

    def _populate_table(self):
        """填充表格数据"""
        # 按记录数量排序
        sorted_stations = sorted(
            self.stations_info.items(),
            key=lambda x: x[1]['record_count'],
            reverse=True
        )

        self.table.setRowCount(len(sorted_stations))

        for row, (station_name, info) in enumerate(sorted_stations):
            # 站点名称
            name_item = QTableWidgetItem(str(station_name))
            self.table.setItem(row, 0, name_item)

            # 记录数量
            count_item = QTableWidgetItem(f"{info['record_count']:,}")
            self.table.setItem(row, 1, count_item)

            # 基础信息状态
            status_item = QTableWidgetItem("✓" if info['has_station_info'] else "✗")
            self.table.setItem(row, 2, status_item)

class MainWindow(QMainWindow):
    """水文降雨流量插值处理软件（GUI框架）。"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("水文降雨流量插值处理程序")

        # 默认数据库路径配置
        self.default_db_paths = {
            'rain': r"C:\Users\<USER>\Desktop\雨量数据.accdb",
            'flow': r"C:\Users\<USER>\Desktop\流量数据.accdb",
            'output': r"C:\Users\<USER>\Desktop\输出数据库.accdb"
        }

        # 获取屏幕尺寸并设置响应式窗口
        screen = QDesktopWidget().screenGeometry()
        self.screen_width, self.screen_height = screen.width(), screen.height()

        # 设置窗口尺寸（80%）和最小尺寸（50%）
        w, h = int(self.screen_width * 0.8), int(self.screen_height * 0.8)
        self.resize(w, h)
        self.setMinimumSize(int(self.screen_width * 0.5), int(self.screen_height * 0.5))
        self.move((self.screen_width - w) // 2, (self.screen_height - h) // 2)
        self._init_ui()

    # ------------------------------------------------------------------
    # 用户界面构建
    # ------------------------------------------------------------------
    def _init_ui(self):
        # 计算响应式尺寸
        sizes = {
            'base_font': max(12, int(self.screen_width * 0.012)),
            'border_radius': max(8, int(self.screen_width * 0.008)),
            'padding': max(12, int(self.screen_width * 0.012)),
            'margin': max(8, int(self.screen_width * 0.008)),
            'button_height': max(40, int(self.screen_height * 0.06))
        }
        sizes.update({
            'large_font': int(sizes['base_font'] * 1.4),
            'button_font': int(sizes['base_font'] * 1.6)
        })

        # 设置样式表
        self.setStyleSheet(self._create_stylesheet(sizes))

        # 创建主布局
        central = QWidget()
        self.setCentralWidget(central)
        main_layout = QHBoxLayout(central)
        main_layout.setContentsMargins(sizes['margin'], sizes['margin'], sizes['margin'], sizes['margin'])
        main_layout.setSpacing(sizes['padding'])

        # 创建左右面板
        self.file_panel = self._create_file_panel(sizes)
        self.data_panel = self._create_data_panel(sizes)

        main_layout.addWidget(self.file_panel, 1)
        main_layout.addWidget(self.data_panel, 2)

    # ------------------------------------------------------------------
    # 辅助方法
    # ------------------------------------------------------------------
    def _create_stylesheet(self, sizes: dict) -> str:
        """创建响应式样式表"""
        c = COLORS  # 简化颜色引用
        return f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 {c['background']}, stop:1 #1a202c);
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif; font-size: {sizes['base_font']}px;
            }}
            QLabel {{ color: {c['text_primary']}; font-size: {sizes['large_font']}px; font-weight: 600; }}
            QLabel[objectName="status_label"] {{
                color: {c['text_secondary']}; font-size: {sizes['base_font']}px; font-weight: 400;
                background: {c['card']}; border: 1px solid {c['border']}; border-radius: {sizes['border_radius']//2}px;
                padding: {sizes['padding']//2}px {sizes['padding']}px; margin: 2px 0px;
            }}
            QLabel[objectName="status_label"][connected="true"] {{
                color: {c['accent']}; border: 1px solid {c['accent']};
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(16,185,129,0.1), stop:1 rgba(5,150,105,0.1));
            }}
            QLabel[objectName="status_label"][connected="false"] {{ color: {c['text_muted']}; border: 1px solid {c['border']}; }}
            QFrame#panel {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['surface']}, stop:1 {c['card']});
                border: 1px solid {c['border']}; border-radius: {sizes['border_radius']}px;
                padding: {sizes['padding']}px; margin: {sizes['margin']}px;
            }}
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['surface']}, stop:1 {c['card']});
                border: 1px solid {c['border']}; border-radius: {sizes['border_radius']}px;
                padding: {sizes['padding']}px {int(sizes['padding']*1.2)}px; color: {c['text_primary']};
                font-size: {sizes['button_font']}px; font-weight: 600; text-align: center; min-height: {sizes['button_height']}px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['primary']}, stop:1 {c['secondary']});
                border: 1px solid {c['primary']}; color: {c['text_primary']};
            }}
            QPushButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['accent']}, stop:1 {c['success']});
                border: 1px solid {c['accent']}; color: {c['text_primary']}; font-weight: 600;
            }}
            QPushButton:pressed {{ background: {c['card']}; border: 1px solid {c['border']}; }}
            QPushButton[objectName="data_btn"] {{
                border-radius: {sizes['border_radius']+2}px; padding: {int(sizes['padding']*1.5)}px {int(sizes['padding']*1.8)}px;
                margin: {sizes['margin']}px; font-size: {int(sizes['button_font']*1.2)}px; min-height: {int(sizes['button_height']*1.2)}px;
            }}
            QPushButton[objectName="data_btn"]:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['secondary']}, stop:1 {c['primary']});
                border: 1px solid {c['secondary']}; font-size: {int(sizes['button_font']*1.2)}px;
            }}
        """

    def _create_file_panel(self, sizes: dict) -> QFrame:
        """创建文件连接面板"""
        panel = QFrame(objectName="panel")
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(*(int(sizes['padding'] * 1.5),) * 4)
        layout.setSpacing(sizes['padding'] // 2)

        # 创建数据库连接组件
        db_types = [("流量", "flow"), ("降雨", "rain"), ("输出", "output")]
        for name, attr in db_types:
            btn = self._make_toggle_button(f"{name}数据库连接", sizes['button_height'])
            status = self._make_status_label(f"未连接{name}数据库")
            setattr(self, f"{attr}_btn", btn)
            setattr(self, f"{attr}_status", status)
            btn.clicked.connect(lambda checked, b=btn, s=status: self._select_db(b, s))
            layout.addWidget(btn)
            layout.addWidget(status)
            layout.addSpacing(sizes['padding'] // 2)

        layout.addStretch(1)

        # 检查并自动连接默认数据库
        self._check_default_databases()

        return panel

    def _create_data_panel(self, sizes: dict) -> QFrame:
        """创建数据处理面板"""
        panel = QFrame(objectName="panel")
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(*(int(sizes['padding'] * 1.5),) * 4)
        layout.setSpacing(sizes['padding'])

        # 创建按钮并绑定不同的功能
        button_configs = [
            ("初始数据展示", self._placeholder),
            ("洪水场次确定", self._placeholder),
            ("降雨数据插值处理", self._placeholder),
            ("流量数据插值处理", self._placeholder),
            ("插值后数据展示", self._placeholder)
        ]

        for name, handler in button_configs:
            btn = QPushButton(name)
            btn.setObjectName("data_btn")
            btn.setMinimumHeight(int(sizes['button_height'] * 1.2))
            btn.clicked.connect(handler)
            layout.addWidget(btn)

        layout.addStretch(1)
        return panel

    def _make_toggle_button(self, text: str, height: int) -> QPushButton:
        btn = QPushButton(text)
        btn.setMinimumHeight(height)
        btn.setCheckable(True)
        return btn

    def _make_status_label(self, text: str) -> QLabel:
        """创建状态标签"""
        label = QLabel(text)
        label.setObjectName("status_label")
        label.setProperty("connected", "false")
        label.setWordWrap(True)
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        return label

    def _check_default_databases(self):
        """检查并自动连接默认数据库路径"""
        print("检查默认数据库路径...")
        for attr, path in self.default_db_paths.items():
            print(f"检查 {attr}: {path}")
            if os.path.exists(path):
                print(f"找到数据库文件: {path}")
                btn = getattr(self, f"{attr}_btn")
                status = getattr(self, f"{attr}_status")
                self._update_status(btn, status, path)
            else:
                print(f"数据库文件不存在: {path}")

    def _update_status(self, btn: QPushButton, status_label: QLabel, path: str = ""):
        """更新按钮和状态标签"""
        if path:
            btn.setChecked(True)
            btn.setToolTip(path)
            status_label.setText(f"已连接: {os.path.basename(path)}")
            status_label.setProperty("connected", "true")
            status_label.setToolTip(path)

            # 如果是雨量数据库连接成功，自动读取并展示数据
            if btn == getattr(self, 'rain_btn', None):
                print(f"雨量数据库连接成功，开始自动读取数据: {path}")
                self._auto_load_rain_data(path)

        else:
            btn.setChecked(False)
            btn.setToolTip("")
            db_type = {"flow": "流量", "rain": "降雨", "output": "输出"}
            for attr, name in db_type.items():
                if btn == getattr(self, f"{attr}_btn"):
                    status_label.setText(f"未连接{name}数据库")
                    break
            status_label.setProperty("connected", "false")
            status_label.setToolTip("")

        # 刷新样式
        status_label.style().unpolish(status_label)
        status_label.style().polish(status_label)

    def _select_db(self, btn: QPushButton, status_label: QLabel):
        """打开文件对话框并更新状态"""
        # 默认打开桌面目录
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        initial_dir = desktop_path if os.path.exists(desktop_path) else os.getcwd()

        path, _ = QFileDialog.getOpenFileName(
            self, "选择数据库文件", initial_dir,
            "Database Files (*.db *.sqlite *.mdb *.accdb);;All Files (*.*)"
        )
        self._update_status(btn, status_label, path)

    def _auto_load_rain_data(self, db_path):
        """自动加载雨量数据并展示"""
        print(f"开始自动加载雨量数据: {db_path}")

        # 创建进度对话框
        self.progress_dialog = QProgressDialog("正在读取雨量数据...", "取消", 0, 0, self)
        self.progress_dialog.setWindowTitle("雨量数据读取")
        self.progress_dialog.setModal(True)
        self.progress_dialog.show()
        print("进度对话框已显示")

        # 创建并启动数据读取线程
        self.rain_reader = RainDataReader(db_path)
        self.rain_reader.progress_updated.connect(self._update_progress, Qt.QueuedConnection)
        self.rain_reader.data_loaded.connect(self._on_data_loaded, Qt.QueuedConnection)
        self.rain_reader.error_occurred.connect(self._on_error, Qt.QueuedConnection)
        self.rain_reader.finished.connect(self._on_reading_finished, Qt.QueuedConnection)
        print("信号连接完成（使用QueuedConnection）")
        self.rain_reader.start()
        print("数据读取线程已启动")

        # 连接取消按钮
        self.progress_dialog.canceled.connect(self._cancel_reading)



    def _update_progress(self, message):
        """更新进度信息"""
        print(f"进度更新: {message}")
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.setLabelText(message)

    def _on_data_loaded(self, stations_info):
        """数据加载完成"""
        try:
            self.stations_info = stations_info

            # 关闭进度对话框
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None

            # 创建并显示站点信息对话框
            self.station_dialog = StationInfoDialog(stations_info, self)

            # 确保对话框不会被垃圾回收
            self.station_dialog.setAttribute(Qt.WA_DeleteOnClose, False)

            # 显示对话框
            self.station_dialog.show()
            self.station_dialog.raise_()
            self.station_dialog.activateWindow()

            print(f"成功显示雨量站信息窗口，包含 {len(stations_info)} 个站点")

        except Exception as e:
            print(f"显示数据时发生错误: {e}")
            QMessageBox.critical(self, "错误", f"显示数据时发生错误: {str(e)}")

    def _on_error(self, error_message):
        """处理错误"""
        QMessageBox.critical(self, "错误", error_message)

    def _on_reading_finished(self):
        """读取完成"""
        # 清理线程引用
        if hasattr(self, 'rain_reader'):
            self.rain_reader.deleteLater()
            delattr(self, 'rain_reader')

    def _cancel_reading(self):
        """取消读取"""
        if hasattr(self, 'rain_reader') and self.rain_reader.isRunning():
            self.rain_reader.terminate()
            self.rain_reader.wait()

    def _placeholder(self):
        """占位符对话框"""
        QMessageBox.information(self, "功能暂未实现", "此功能正在开发中，敬请期待！")


# 应用程序入口点
if __name__ == "__main__":
    print("启动水文降雨流量处理程序...")
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon())  # 占位符：如果有图标文件可在此添加路径

    print("创建主窗口...")
    window = MainWindow()
    window.show()
    print("主窗口已显示")

    sys.exit(app.exec_())
