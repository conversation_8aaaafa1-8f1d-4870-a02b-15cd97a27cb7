import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, QPushButton,
    QFileDialog, QMessageBox, QFrame, QDesktopWidget, QLabel
)
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtCore import Qt

# 导入数据读取模块
from 雨量读取 import RainDataManager
from 流量读取 import FlowDataManager

# 现代深色主题配色方案
COLORS = {
    'primary': "#2563EB", 'secondary': "#3B82F6", 'accent': "#10B981", 'success': "#059669",
    'warning': "#F59E0B", 'danger': "#EF4444", 'background': "#0F172A", 'surface': "#1E293B",
    'card': "#334155", 'border': "#475569", 'text_primary': "#F8FAFC",
    'text_secondary': "#CBD5E1", 'text_muted': "#94A3B8"
}



# -----------------------------------------------------------
# Main Window
# -----------------------------------------------------------
class MainWindow(QMainWindow):
    """水文降雨流量插值处理软件（GUI框架）。"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("水文降雨流量插值处理程序")

        # 默认数据库路径配置
        self.default_db_paths = {
            'rain': r"C:\Users\<USER>\Desktop\雨量数据.accdb",
            'flow': r"C:\Users\<USER>\Desktop\流量数据.accdb",
            'output': r"C:\Users\<USER>\Desktop\输出数据库.accdb"
        }

        # 获取屏幕尺寸并设置响应式窗口
        screen = QDesktopWidget().screenGeometry()
        self.screen_width, self.screen_height = screen.width(), screen.height()

        # 设置窗口尺寸（80%）和最小尺寸（50%）
        w, h = int(self.screen_width * 0.8), int(self.screen_height * 0.8)
        self.resize(w, h)
        self.setMinimumSize(int(self.screen_width * 0.5), int(self.screen_height * 0.5))
        self.move((self.screen_width - w) // 2, (self.screen_height - h) // 2)
        self._init_ui()

    # ------------------------------------------------------------------
    # 用户界面构建
    # ------------------------------------------------------------------
    def _init_ui(self):
        # 计算响应式尺寸
        sizes = {
            'base_font': max(12, int(self.screen_width * 0.012)),
            'border_radius': max(8, int(self.screen_width * 0.008)),
            'padding': max(12, int(self.screen_width * 0.012)),
            'margin': max(8, int(self.screen_width * 0.008)),
            'button_height': max(40, int(self.screen_height * 0.06))
        }
        sizes.update({
            'large_font': int(sizes['base_font'] * 1.4),
            'button_font': int(sizes['base_font'] * 1.6)
        })

        # 设置样式表
        self.setStyleSheet(self._create_stylesheet(sizes))

        # 创建主布局
        central = QWidget()
        self.setCentralWidget(central)
        main_layout = QHBoxLayout(central)
        main_layout.setContentsMargins(sizes['margin'], sizes['margin'], sizes['margin'], sizes['margin'])
        main_layout.setSpacing(sizes['padding'])

        # 创建左右面板
        self.file_panel = self._create_file_panel(sizes)
        self.data_panel = self._create_data_panel(sizes)

        main_layout.addWidget(self.file_panel, 1)
        main_layout.addWidget(self.data_panel, 2)

        # 检查并自动连接默认数据库
        self._check_default_databases()

    # ------------------------------------------------------------------
    # 辅助方法
    # ------------------------------------------------------------------
    def _create_stylesheet(self, sizes: dict) -> str:
        """创建响应式样式表"""
        c = COLORS  # 简化颜色引用
        return f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 {c['background']}, stop:1 #1a202c);
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif; font-size: {sizes['base_font']}px;
            }}
            QLabel {{ color: {c['text_primary']}; font-size: {sizes['large_font']}px; font-weight: 600; }}
            QLabel[objectName="status_label"] {{
                color: {c['text_secondary']}; font-size: {sizes['base_font']}px; font-weight: 400;
                background: {c['card']}; border: 1px solid {c['border']}; border-radius: {sizes['border_radius']//2}px;
                padding: {sizes['padding']//2}px {sizes['padding']}px; margin: 2px 0px;
            }}
            QLabel[objectName="status_label"][connected="true"] {{
                color: {c['accent']}; border: 1px solid {c['accent']};
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(16,185,129,0.1), stop:1 rgba(5,150,105,0.1));
            }}
            QLabel[objectName="status_label"][connected="false"] {{ color: {c['text_muted']}; border: 1px solid {c['border']}; }}
            QFrame#panel {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['surface']}, stop:1 {c['card']});
                border: 1px solid {c['border']}; border-radius: {sizes['border_radius']}px;
                padding: {sizes['padding']}px; margin: {sizes['margin']}px;
            }}
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['surface']}, stop:1 {c['card']});
                border: 1px solid {c['border']}; border-radius: {sizes['border_radius']}px;
                padding: {sizes['padding']}px {int(sizes['padding']*1.2)}px; color: {c['text_primary']};
                font-size: {sizes['button_font']}px; font-weight: 600; text-align: center; min-height: {sizes['button_height']}px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['primary']}, stop:1 {c['secondary']});
                border: 1px solid {c['primary']}; color: {c['text_primary']};
            }}
            QPushButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['accent']}, stop:1 {c['success']});
                border: 1px solid {c['accent']}; color: {c['text_primary']}; font-weight: 600;
            }}
            QPushButton:pressed {{ background: {c['card']}; border: 1px solid {c['border']}; }}
            QPushButton[objectName="data_btn"] {{
                border-radius: {sizes['border_radius']+2}px; padding: {int(sizes['padding']*1.5)}px {int(sizes['padding']*1.8)}px;
                margin: {sizes['margin']}px; font-size: {int(sizes['button_font']*1.2)}px; min-height: {int(sizes['button_height']*1.2)}px;
            }}
            QPushButton[objectName="data_btn"]:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {c['secondary']}, stop:1 {c['primary']});
                border: 1px solid {c['secondary']}; font-size: {int(sizes['button_font']*1.2)}px;
            }}
        """

    def _create_file_panel(self, sizes: dict) -> QFrame:
        """创建文件连接面板"""
        panel = QFrame(objectName="panel")
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(*(int(sizes['padding'] * 1.5),) * 4)
        layout.setSpacing(sizes['padding'] // 2)

        # 创建数据库连接组件
        db_types = [("流量", "flow"), ("降雨", "rain"), ("输出", "output")]
        for name, attr in db_types:
            btn = self._make_toggle_button(f"{name}数据库连接", sizes['button_height'])
            status = self._make_status_label(f"未连接{name}数据库")
            setattr(self, f"{attr}_btn", btn)
            setattr(self, f"{attr}_status", status)
            btn.clicked.connect(lambda checked, b=btn, s=status: self._select_db(b, s))
            layout.addWidget(btn)
            layout.addWidget(status)
            layout.addSpacing(sizes['padding'] // 2)

        layout.addStretch(1)
        return panel

    def _create_data_panel(self, sizes: dict) -> QFrame:
        """创建数据处理面板"""
        panel = QFrame(objectName="panel")
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(*(int(sizes['padding'] * 1.5),) * 4)
        layout.setSpacing(sizes['padding'])

        # 创建按钮并绑定不同的功能
        button_configs = [
            ("初始数据展示", self._placeholder),
            ("洪水场次确定", self._placeholder),
            ("降雨数据插值处理", self._placeholder),
            ("流量数据插值处理", self._placeholder),
            ("插值后数据展示", self._placeholder)
        ]

        for name, handler in button_configs:
            btn = QPushButton(name)
            btn.setObjectName("data_btn")
            btn.setMinimumHeight(int(sizes['button_height'] * 1.2))
            btn.clicked.connect(handler)
            layout.addWidget(btn)

        layout.addStretch(1)
        return panel

    def _make_toggle_button(self, text: str, height: int) -> QPushButton:
        btn = QPushButton(text)
        btn.setMinimumHeight(height)
        btn.setCheckable(True)
        return btn

    def _make_status_label(self, text: str) -> QLabel:
        """创建状态标签"""
        label = QLabel(text)
        label.setObjectName("status_label")
        label.setProperty("connected", "false")
        label.setWordWrap(True)
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        return label

    def _select_db(self, btn: QPushButton, status: QLabel):
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        path, _ = QFileDialog.getOpenFileName(
            self, "选择数据库文件", desktop,
            "Database Files (*.accdb *.mdb *.db *.sqlite);;All Files(*)")
        if path: self._update_status(btn, status, path)

    def _update_status(self, btn: QPushButton, status_label: QLabel, path: str = ""):
        """更新按钮和状态标签"""
        if path:
            btn.setChecked(True)
            btn.setToolTip(path)
            status_label.setText(f"已连接: {os.path.basename(path)}")
            status_label.setProperty("connected", "true")
            status_label.setToolTip(path)

            # 如果是雨量数据库连接成功，自动读取并展示数据
            if btn == getattr(self, 'rain_btn', None):
                self.rain_manager = RainDataManager(self)
                self.rain_manager.load_data(path)
            elif btn == getattr(self, 'flow_btn', None):
                self.flow_manager = FlowDataManager(self)
                self.flow_manager.load_data(path)

        else:
            btn.setChecked(False)
            btn.setToolTip("")
            db_type = {"flow": "流量", "rain": "降雨", "output": "输出"}
            for attr, name in db_type.items():
                if btn == getattr(self, f"{attr}_btn"):
                    status_label.setText(f"未连接{name}数据库")
                    break
            status_label.setProperty("connected", "false")
            status_label.setToolTip("")

        # 刷新样式
        status_label.style().unpolish(status_label)
        status_label.style().polish(status_label)

    def _check_default_databases(self):
        for key, path in self.default_db_paths.items():
            if os.path.exists(path):
                btn = getattr(self, f"{key}_btn"); status = getattr(self, f"{key}_status")
                self._update_status(btn, status, path)



    def _placeholder(self):
        """占位符对话框"""
        QMessageBox.information(self, "功能暂未实现", "此功能正在开发中，敬请期待！")

# -----------------------------------------------------------
# 程序入口
# -----------------------------------------------------------
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon())   # 如有自定义图标可在此加载
    win = MainWindow(); win.show()
    sys.exit(app.exec_())
