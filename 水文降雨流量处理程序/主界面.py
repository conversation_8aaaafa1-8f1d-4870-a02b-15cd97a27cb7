import sys
import os
import pyodbc
import pandas as pd
import numpy as np
from pathlib import Path

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, QPushButton,
    QFileDialog, QMessageBox, QFrame, QDesktopWidget, QLabel, QDialog,
    QTableWidget, QTableWidgetItem, QHeaderView, QProgressDialog, QScrollArea
)
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtCore import Qt, QThread, pyqtSignal

# -----------------------------------------------------------
# 颜色主题
# -----------------------------------------------------------
COLORS = {
    'primary': "#2563EB", 'secondary': "#3B82F6", 'accent': "#10B981", 'success': "#059669",
    'warning': "#F59E0B", 'danger': "#EF4444", 'background': "#0F172A", 'surface': "#1E293B",
    'card': "#334155", 'border': "#475569", 'text_primary': "#F8FAFC",
    'text_secondary': "#CBD5E1", 'text_muted': "#94A3B8"
}

# -----------------------------------------------------------
# RainDataReader —— 读取雨量数据（已存在）
# -----------------------------------------------------------
class RainDataReader(QThread):
    progress_updated = pyqtSignal(str)
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, db_path):
        super().__init__()
        self.db_path = db_path

    def run(self):
        try:
            self.progress_updated.emit("正在连接雨量数据库…")
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                fr"DBQ={self.db_path}"
            )
            with pyodbc.connect(conn_str) as conn:
                self.progress_updated.emit("数据库连接成功，开始读取数据…")
                df_station = pd.read_sql("SELECT * FROM [P-Station]", conn)
                df_data = pd.read_sql("SELECT * FROM [表]", conn)

            self.progress_updated.emit("正在处理数据…")
            station_arr = df_station.values
            data_arr = df_data.values
            key_col = 2
            keys = np.unique(data_arr[:, key_col])

            stations_info = {}
            for k in keys:
                recs = data_arr[data_arr[:, key_col] == k]
                station_info_row = next((row for row in station_arr if str(row[2]) == str(k)), None)
                stations_info[k] = {
                    'name': str(k),
                    'record_count': recs.shape[0],
                    'has_station_info': station_info_row is not None
                }

            self.data_loaded.emit(stations_info)

        except Exception as e:
            self.error_occurred.emit(f"读取数据时发生错误：{e}")

# -----------------------------------------------------------
# FlowDataReader —— 新增：读取 a01流量站表，仅提取站名
# -----------------------------------------------------------
class FlowDataReader(QThread):
    progress_updated = pyqtSignal(str)
    data_loaded = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def __init__(self, db_path):
        super().__init__()
        self.db_path = db_path

    def run(self):
        try:
            self.progress_updated.emit("正在连接流量数据库…")
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                fr"DBQ={self.db_path}"
            )
            with pyodbc.connect(conn_str) as conn:
                self.progress_updated.emit("连接成功，读取 a01流量站表…")
                df = pd.read_sql("SELECT * FROM [a01流量站表]", conn)

            stations = df.iloc[:, 1].dropna().astype(str).tolist()
            self.progress_updated.emit(f"读取完成，共 {len(stations)} 个站点")
            self.data_loaded.emit(stations)

        except Exception as e:
            self.error_occurred.emit(f"读取流量数据时发生错误：{e}")

# -----------------------------------------------------------
# StationInfoDialog —— 雨量站信息表格（已存在）
# -----------------------------------------------------------
class StationInfoDialog(QDialog):
    def __init__(self, stations_info, parent=None):
        super().__init__(parent)
        self.stations_info = stations_info
        self.setWindowTitle(f"雨量站信息 - 共 {len(stations_info)} 个站点")
        self.setModal(False)

        scr = QDesktopWidget().screenGeometry()
        sw, sh = scr.width(), scr.height()
        self.resize(int(sw * 0.6), int(sh * 0.7))
        self.move((sw - self.width()) // 2, (sh - self.height()) // 2)

        layout = QVBoxLayout(self)
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["站点名称", "记录数量", "基础信息"])
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Fixed)
        self.table.setColumnWidth(1, 120)
        self.table.setColumnWidth(2, 100)

        self._fill()
        layout.addWidget(self.table)

    def _fill(self):
        sorted_items = sorted(self.stations_info.items(),
                              key=lambda x: x[1]['record_count'], reverse=True)
        self.table.setRowCount(len(sorted_items))
        for r, (name, info) in enumerate(sorted_items):
            self.table.setItem(r, 0, QTableWidgetItem(str(name)))
            self.table.setItem(r, 1, QTableWidgetItem(f"{info['record_count']:,}"))
            self.table.setItem(r, 2, QTableWidgetItem("✓" if info['has_station_info'] else "✗"))

# -----------------------------------------------------------
# FlowStationDialog —— 新增：按钮方式列出流量站
# -----------------------------------------------------------
class FlowStationDialog(QDialog):
    def __init__(self, station_names, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"流量站选择 – 共 {len(station_names)} 站")
        self.setModal(False)

        scr = QDesktopWidget().screenGeometry()
        sw, sh = scr.width(), scr.height()
        self.resize(int(sw * 0.4), int(sh * 0.6))
        self.move((sw - self.width()) // 2, (sh - self.height()) // 2)
        self.setStyleSheet("background-color:#f0f4f8;")

        layout = QVBoxLayout(self)
        title = QLabel("点击选择一个流量站")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size:18px;font-weight:bold;color:#1e293b;")
        layout.addWidget(title)

        scroll = QScrollArea(); scroll.setWidgetResizable(True)
        inner = QWidget(); ivl = QVBoxLayout(inner)

        for name in station_names:
            btn = QPushButton(name)
            btn.setStyleSheet("""
                QPushButton{
                    background-color:#3b82f6;color:white;padding:10px;
                    border:none;border-radius:8px;margin-bottom:8px;
                }
                QPushButton:hover{background-color:#2563eb;}
            """)
            btn.clicked.connect(lambda _, n=name: QMessageBox.information(self, "已选择", f"你选择了：{n}"))
            ivl.addWidget(btn)
        ivl.addStretch()
        scroll.setWidget(inner)
        layout.addWidget(scroll)

# -----------------------------------------------------------
# Main Window
# -----------------------------------------------------------
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("水文降雨流量插值处理程序")

        self.default_db_paths = {
            'rain':  r"C:\Users\<USER>\Desktop\雨量数据.accdb",
            'flow':  r"C:\Users\<USER>\Desktop\流量数据.accdb",
            'output': r"C:\Users\<USER>\Desktop\输出数据库.accdb"
        }

        scr = QDesktopWidget().screenGeometry()
        self.screen_width, self.screen_height = scr.width(), scr.height()
        w, h = int(self.screen_width * 0.8), int(self.screen_height * 0.8)
        self.resize(w, h); self.move((self.screen_width - w)//2, (self.screen_height - h)//2)

        self._init_ui()
        self._check_default_databases()

    # ----------------------- UI 构建 ------------------------
    def _init_ui(self):
        sizes = {
            'padding': 12, 'margin': 8, 'button_h': 45,
            'font': 13, 'bigfont': 17
        }
        central = QWidget(); self.setCentralWidget(central)
        main = QHBoxLayout(central); main.setContentsMargins(8, 8, 8, 8)
        # 左侧 面板
        self.file_panel = QFrame(); main.addWidget(self.file_panel, 1)
        fp_layout = QVBoxLayout(self.file_panel)
        fp_layout.setSpacing(6)

        # 连接按钮 & 状态
        for txt, key in [("流量", "flow"), ("降雨", "rain"), ("输出", "output")]:
            btn = QPushButton(f"连接{txt}数据库"); btn.setCheckable(True)
            st = QLabel(f"未连接{txt}数据库"); st.setProperty("connected", "false")
            setattr(self, f"{key}_btn", btn); setattr(self, f"{key}_status", st)
            btn.clicked.connect(lambda _, b=btn, s=st: self._select_db(b, s))
            fp_layout.addWidget(btn); fp_layout.addWidget(st)

        fp_layout.addStretch()
        # 右侧 占位
        right = QFrame(); main.addWidget(right, 3)

    # ----------------------- 功能 --------------------------
    def _select_db(self, btn: QPushButton, status: QLabel):
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        path, _ = QFileDialog.getOpenFileName(
            self, "选择数据库文件", desktop,
            "Database Files (*.accdb *.mdb *.db *.sqlite);;All Files(*)")
        if path: self._update_status(btn, status, path)

    def _update_status(self, btn: QPushButton, status: QLabel, path: str = ""):
        connected = bool(path)
        status.setProperty("connected", "true" if connected else "false")
        status.setText(f"{'已连接' if connected else '未连接'}: {os.path.basename(path) if connected else ''}")
        btn.setChecked(connected); btn.setToolTip(path)

        # 自动触发读取
        if connected and btn is getattr(self, 'rain_btn'):
            self._auto_load_rain_data(path)
        if connected and btn is getattr(self, 'flow_btn'):
            self._auto_load_flow_data(path)

    def _check_default_databases(self):
        for key, path in self.default_db_paths.items():
            if os.path.exists(path):
                btn = getattr(self, f"{key}_btn"); status = getattr(self, f"{key}_status")
                self._update_status(btn, status, path)

    # ---------- 雨量读取 ----------
    def _auto_load_rain_data(self, db_path):
        self.progress_dialog = QProgressDialog("正在读取雨量数据…", "取消", 0, 0, self)
        self.progress_dialog.setWindowTitle("雨量数据读取"); self.progress_dialog.show()

        self.rain_reader = RainDataReader(db_path)
        self.rain_reader.progress_updated.connect(self.progress_dialog.setLabelText, Qt.QueuedConnection)
        self.rain_reader.data_loaded.connect(self._on_rain_loaded, Qt.QueuedConnection)
        self.rain_reader.error_occurred.connect(lambda e: QMessageBox.critical(self, "错误", e), Qt.QueuedConnection)
        self.rain_reader.finished.connect(self._cleanup_rain_reader, Qt.QueuedConnection)
        self.rain_reader.start()
        self.progress_dialog.canceled.connect(self._cancel_rain_reader)

    def _cleanup_rain_reader(self):
        """清理雨量数据读取线程"""
        if hasattr(self, 'rain_reader'):
            self.rain_reader.deleteLater()
            delattr(self, 'rain_reader')

    def _cancel_rain_reader(self):
        """取消雨量数据读取"""
        if hasattr(self, 'rain_reader') and self.rain_reader.isRunning():
            self.rain_reader.terminate()
            self.rain_reader.wait(3000)  # 等待最多3秒

    def _on_rain_loaded(self, info_dict):
        try:
            print(f"雨量数据加载完成，收到{len(info_dict)}个站点")
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None

            # 使用QTimer延迟创建对话框，确保在主线程中执行
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(100, lambda: self._create_rain_dialog(info_dict))

        except Exception as e:
            print(f"处理雨量数据时发生错误: {e}")
            QMessageBox.critical(self, "错误", f"处理雨量数据时发生错误: {str(e)}")

    def _create_rain_dialog(self, info_dict):
        """在主线程中安全创建雨量站对话框"""
        try:
            self.rain_dialog = StationInfoDialog(info_dict, self)
            self.rain_dialog.setAttribute(Qt.WA_DeleteOnClose, False)
            self.rain_dialog.show()
            self.rain_dialog.raise_()
            self.rain_dialog.activateWindow()
            print(f"雨量站信息窗口显示成功")
        except Exception as e:
            print(f"创建雨量站对话框时发生错误: {e}")
            QMessageBox.critical(self, "错误", f"创建雨量站对话框时发生错误: {str(e)}")

    # ---------- 流量读取 ----------
    def _auto_load_flow_data(self, db_path):
        self.flow_progress = QProgressDialog("正在读取流量站信息…", "取消", 0, 0, self)
        self.flow_progress.setWindowTitle("流量数据读取"); self.flow_progress.show()

        self.flow_reader = FlowDataReader(db_path)
        self.flow_reader.progress_updated.connect(self.flow_progress.setLabelText, Qt.QueuedConnection)
        self.flow_reader.data_loaded.connect(self._on_flow_loaded, Qt.QueuedConnection)
        self.flow_reader.error_occurred.connect(lambda e: QMessageBox.critical(self, "错误", e), Qt.QueuedConnection)
        self.flow_reader.finished.connect(self._cleanup_flow_reader, Qt.QueuedConnection)
        self.flow_reader.start()
        self.flow_progress.canceled.connect(self._cancel_flow_reader)

    def _cleanup_flow_reader(self):
        """清理流量数据读取线程"""
        if hasattr(self, 'flow_reader'):
            self.flow_reader.deleteLater()
            delattr(self, 'flow_reader')

    def _cancel_flow_reader(self):
        """取消流量数据读取"""
        if hasattr(self, 'flow_reader') and self.flow_reader.isRunning():
            self.flow_reader.terminate()
            self.flow_reader.wait(3000)  # 等待最多3秒

    def _on_flow_loaded(self, stations):
        try:
            print(f"流量数据加载完成，收到{len(stations)}个站点")
            if hasattr(self, 'flow_progress') and self.flow_progress:
                self.flow_progress.close()
                self.flow_progress = None

            # 使用QTimer延迟创建对话框，确保在主线程中执行
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(100, lambda: self._create_flow_dialog(stations))

        except Exception as e:
            print(f"处理流量数据时发生错误: {e}")
            QMessageBox.critical(self, "错误", f"处理流量数据时发生错误: {str(e)}")

    def _create_flow_dialog(self, stations):
        """在主线程中安全创建流量站对话框"""
        try:
            self.flow_dialog = FlowStationDialog(stations, self)
            self.flow_dialog.setAttribute(Qt.WA_DeleteOnClose, False)
            self.flow_dialog.show()
            self.flow_dialog.raise_()
            self.flow_dialog.activateWindow()
            print(f"流量站信息窗口显示成功")
        except Exception as e:
            print(f"创建流量站对话框时发生错误: {e}")
            QMessageBox.critical(self, "错误", f"创建流量站对话框时发生错误: {str(e)}")

# -----------------------------------------------------------
# 程序入口
# -----------------------------------------------------------
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon())   # 如有自定义图标可在此加载
    win = MainWindow(); win.show()
    sys.exit(app.exec_())
