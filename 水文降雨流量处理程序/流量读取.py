import sys, os, pyodbc, pandas as pd, numpy as np
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QHB<PERSON>Layout, QVBoxLayout, QPushButton,
    QFileDialog, QMessageBox, QFrame, QDesktopWidget, Q<PERSON><PERSON><PERSON>, QDialog,
    QTableWidget, QTableWidgetItem, QHeaderView, QProgressDialog, QScrollArea
)
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt, QThread, pyqtSignal

# -----------------------------------------------------------
# 颜色主题
# -----------------------------------------------------------
COLORS = {
    'primary': "#2563EB", 'secondary': "#3B82F6", 'accent': "#10B981",
    'background': "#0F172A", 'surface': "#1E293B", 'card': "#334155",
    'border': "#475569", 'text_primary': "#F8FAFC", 'text_muted': "#94A3B8"
}

# -----------------------------------------------------------
# RainDataReader —— 读取雨量信息
# -----------------------------------------------------------
class RainDataReader(QThread):
    progress_updated = pyqtSignal(str)
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, db_path):
        super().__init__(); self.db_path = db_path

    def run(self):
        try:
            self.progress_updated.emit("连接雨量数据库…")
            conn = pyodbc.connect(
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                fr"DBQ={self.db_path}"
            )
            self.progress_updated.emit("读取数据…")
            df_station = pd.read_sql("SELECT * FROM [P-Station]", conn)
            df_data    = pd.read_sql("SELECT * FROM [表]", conn)
            conn.close()

            key_col = 2
            keys = np.unique(df_data.values[:, key_col])
            station_arr = df_station.values

            info = {}
            for k in keys:
                recs = df_data[df_data.iloc[:, key_col] == k]
                base = next((row for row in station_arr if str(row[2]) == str(k)), None)
                info[k] = dict(name=str(k), record_count=len(recs),
                               has_station_info=base is not None)
            self.data_loaded.emit(info)
        except Exception as e:
            self.error_occurred.emit(f"读取雨量数据错误：{e}")

# -----------------------------------------------------------
# FlowDataReader —— 仅提取流量站名
# -----------------------------------------------------------
class FlowDataReader(QThread):
    progress_updated = pyqtSignal(str)
    data_loaded = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def __init__(self, db_path):
        super().__init__(); self.db_path = db_path

    def run(self):
        try:
            self.progress_updated.emit("连接流量数据库…")
            conn = pyodbc.connect(
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                fr"DBQ={self.db_path}"
            )
            self.progress_updated.emit("读取 a01流量站表…")
            df = pd.read_sql("SELECT * FROM [a01流量站表]", conn)
            conn.close()
            stations = df.iloc[:, 1].dropna().astype(str).tolist()
            self.data_loaded.emit(stations)
        except Exception as e:
            self.error_occurred.emit(f"读取流量站错误：{e}")

# -----------------------------------------------------------
# StationInfoDialog —— 雨量站统计表
# -----------------------------------------------------------
class StationInfoDialog(QDialog):
    def __init__(self, info_dict, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"雨量站信息（{len(info_dict)} 个）")
        scr = QDesktopWidget().screenGeometry()
        self.resize(int(scr.width()*0.6), int(scr.height()*0.7))

        layout = QVBoxLayout(self)
        table = QTableWidget(); layout.addWidget(table)
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["站点", "记录数", "基础信息"])
        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        table.setRowCount(len(info_dict))
        for r,(k,v) in enumerate(sorted(info_dict.items(),
                                        key=lambda x:x[1]['record_count'], reverse=True)):
            table.setItem(r,0,QTableWidgetItem(str(k)))
            table.setItem(r,1,QTableWidgetItem(f"{v['record_count']:,}"))
            table.setItem(r,2,QTableWidgetItem("✓" if v['has_station_info'] else "✗"))

# -----------------------------------------------------------
# FlowTableDialog —— 展示“站名流量”表
# -----------------------------------------------------------
class FlowTableDialog(QDialog):
    def __init__(self, df: pd.DataFrame, station_name: str, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"{station_name} 流量表（共 {len(df)} 条）")
        scr = QDesktopWidget().screenGeometry()
        self.resize(int(scr.width()*0.5), int(scr.height()*0.7))

        layout = QVBoxLayout(self)
        table = QTableWidget(); layout.addWidget(table)
        table.setColumnCount(2)
        table.setHorizontalHeaderLabels(["时间", "流量"])
        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        table.setRowCount(len(df))

        for r,(t,q) in enumerate(zip(df.iloc[:,0], df.iloc[:,1])):
            table.setItem(r,0,QTableWidgetItem(str(t)))
            table.setItem(r,1,QTableWidgetItem(f"{q:.3f}" if pd.notna(q) else ""))

# -----------------------------------------------------------
# FlowStationDialog —— 列出流量站按钮 & 查询对应表
# -----------------------------------------------------------
class FlowStationDialog(QDialog):
    def __init__(self, station_names, db_path, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.setWindowTitle(f"流量站选择（{len(station_names)} 站）")
        scr = QDesktopWidget().screenGeometry()
        self.resize(int(scr.width()*0.4), int(scr.height()*0.6))

        layout = QVBoxLayout(self)
        scroll = QScrollArea(); scroll.setWidgetResizable(True)
        inner = QWidget(); il = QVBoxLayout(inner)

        for name in station_names:
            btn = QPushButton(name)
            btn.setStyleSheet("""
                QPushButton{
                    background:#3b82f6;color:white;padding:10px;
                    border:none;border-radius:8px;margin-bottom:8px;
                }
                QPushButton:hover{background:#2563eb;}
            """)
            btn.clicked.connect(lambda _, n=name: self._load_flow_table(n))
            il.addWidget(btn)
        il.addStretch(); scroll.setWidget(inner)
        layout.addWidget(scroll)

    # ---------- 核心：读取“站名流量”表并展示 ----------
    def _load_flow_table(self, station_name):
        table_name = f"{station_name}流量"
        try:
            conn = pyodbc.connect(
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                fr"DBQ={self.db_path}"
            )
            df = pd.read_sql(f"SELECT * FROM [{table_name}]", conn)
            conn.close()
            if df.empty:
                QMessageBox.information(self, "提示", f"表 {table_name} 无数据。")
                return
            dlg = FlowTableDialog(df, station_name, self)
            dlg.setAttribute(Qt.WA_DeleteOnClose, False)
            dlg.show(); dlg.raise_(); dlg.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取表 {table_name} 失败：{e}")

# -----------------------------------------------------------
# MainWindow
# -----------------------------------------------------------
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("水文降雨流量插值处理程序")

        self.default_db_paths = {
            'rain':  r"C:\Users\<USER>\Desktop\雨量数据.accdb",
            'flow':  r"C:\Users\<USER>\Desktop\流量数据.accdb",
            'output':r"C:\Users\<USER>\Desktop\输出数据库.accdb"
        }

        scr = QDesktopWidget().screenGeometry()
        self.resize(int(scr.width()*0.8), int(scr.height()*0.8))
        self.move((scr.width()-self.width())//2, (scr.height()-self.height())//2)

        self._init_ui()
        self._check_default_databases()

    # -------------------- UI --------------------
    def _init_ui(self):
        central = QWidget(); self.setCentralWidget(central)
        main = QHBoxLayout(central)
        self.flow_btn, self.flow_status = self._add_db_control(main, "流量", "flow")
        self.rain_btn, self.rain_status = self._add_db_control(main, "降雨", "rain")
        self.output_btn,  self.output_status  = self._add_db_control(main, "输出", "output")

    def _add_db_control(self, layout, label, key):
        panel = QFrame(); v = QVBoxLayout(panel)
        btn = QPushButton(f"连接{label}数据库"); btn.setCheckable(True)
        status = QLabel(f"未连接{label}数据库"); status.setProperty("connected","false")
        btn.clicked.connect(lambda _, b=btn, s=status: self._select_db(b,s))
        v.addWidget(btn); v.addWidget(status); layout.addWidget(panel,1)
        return btn, status

    # -------------------- 选择 / 自动连接 --------------------
    def _select_db(self, btn, status):
        path, _ = QFileDialog.getOpenFileName(
            self, "选择数据库", os.path.join(os.path.expanduser("~"),"Desktop"),
            "Database Files (*.accdb *.mdb *.db *.sqlite);;All Files(*)"
        )
        if path: self._update_status(btn, status, path)

    def _update_status(self, btn, status, path=""):
        ok = bool(path)
        status.setText((f"已连接: {os.path.basename(path)}") if ok else "未连接")
        status.setProperty("connected","true" if ok else "false")
        btn.setChecked(ok); btn.setToolTip(path)

        if ok and btn is self.flow_btn:  self._auto_load_flow(path)
        if ok and btn is self.rain_btn:  self._auto_load_rain(path)

    def _check_default_databases(self):
        for key, path in self.default_db_paths.items():
            if os.path.exists(path):
                btn = getattr(self,f"{key}_btn"); st = getattr(self,f"{key}_status")
                self._update_status(btn, st, path)

    # -------------------- 雨量流程 --------------------
    def _auto_load_rain(self, db_path):
        self.rain_progress = QProgressDialog("读取雨量数据…","取消",0,0,self)
        self.rain_progress.show()

        self.rain_reader = RainDataReader(db_path)
        self.rain_reader.progress_updated.connect(self.rain_progress.setLabelText, Qt.QueuedConnection)
        self.rain_reader.data_loaded.connect(lambda d: self._show_rain(d, self.rain_progress), Qt.QueuedConnection)
        self.rain_reader.error_occurred.connect(lambda e: QMessageBox.critical(self,"错误",e), Qt.QueuedConnection)
        self.rain_reader.finished.connect(self._cleanup_rain_reader, Qt.QueuedConnection)
        self.rain_reader.start()
        self.rain_progress.canceled.connect(self._cancel_rain_reader)

    def _cleanup_rain_reader(self):
        """清理雨量数据读取线程"""
        if hasattr(self, 'rain_reader'):
            self.rain_reader.deleteLater()
            delattr(self, 'rain_reader')

    def _cancel_rain_reader(self):
        """取消雨量数据读取"""
        if hasattr(self, 'rain_reader') and self.rain_reader.isRunning():
            self.rain_reader.terminate()
            self.rain_reader.wait(3000)

    def _show_rain(self, info_dict, pdialog):
        try:
            pdialog.close()
            # 使用QTimer延迟创建对话框，确保在主线程中执行
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(100, lambda: self._create_rain_dialog(info_dict))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示雨量数据时发生错误: {str(e)}")

    def _create_rain_dialog(self, info_dict):
        """在主线程中安全创建雨量站对话框"""
        try:
            self.rain_dialog = StationInfoDialog(info_dict, self)
            self.rain_dialog.setAttribute(Qt.WA_DeleteOnClose, False)
            self.rain_dialog.show()
            self.rain_dialog.raise_()
            self.rain_dialog.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建雨量站对话框时发生错误: {str(e)}")

    # -------------------- 流量流程 --------------------
    def _auto_load_flow(self, db_path):
        self.flow_progress = QProgressDialog("读取流量站列表…","取消",0,0,self)
        self.flow_progress.show()

        self.flow_reader = FlowDataReader(db_path)
        self.flow_reader.progress_updated.connect(self.flow_progress.setLabelText, Qt.QueuedConnection)
        self.flow_reader.data_loaded.connect(lambda lst: self._show_flow(lst, db_path, self.flow_progress), Qt.QueuedConnection)
        self.flow_reader.error_occurred.connect(lambda e: QMessageBox.critical(self,"错误",e), Qt.QueuedConnection)
        self.flow_reader.finished.connect(self._cleanup_flow_reader, Qt.QueuedConnection)
        self.flow_reader.start()
        self.flow_progress.canceled.connect(self._cancel_flow_reader)

    def _cleanup_flow_reader(self):
        """清理流量数据读取线程"""
        if hasattr(self, 'flow_reader'):
            self.flow_reader.deleteLater()
            delattr(self, 'flow_reader')

    def _cancel_flow_reader(self):
        """取消流量数据读取"""
        if hasattr(self, 'flow_reader') and self.flow_reader.isRunning():
            self.flow_reader.terminate()
            self.flow_reader.wait(3000)

    def _show_flow(self, stations, db_path, pdialog):
        try:
            pdialog.close()
            # 使用QTimer延迟创建对话框，确保在主线程中执行
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(100, lambda: self._create_flow_dialog(stations, db_path))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示流量数据时发生错误: {str(e)}")

    def _create_flow_dialog(self, stations, db_path):
        """在主线程中安全创建流量站对话框"""
        try:
            self.flow_dialog = FlowStationDialog(stations, db_path, self)
            self.flow_dialog.setAttribute(Qt.WA_DeleteOnClose, False)
            self.flow_dialog.show()
            self.flow_dialog.raise_()
            self.flow_dialog.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建流量站对话框时发生错误: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭时清理所有线程"""
        try:
            # 清理雨量数据读取线程
            if hasattr(self, 'rain_reader') and self.rain_reader.isRunning():
                self.rain_reader.terminate()
                self.rain_reader.wait(3000)

            # 清理流量数据读取线程
            if hasattr(self, 'flow_reader') and self.flow_reader.isRunning():
                self.flow_reader.terminate()
                self.flow_reader.wait(3000)

            event.accept()
        except Exception as e:
            print(f"关闭窗口时发生错误: {e}")
            event.accept()

# -----------------------------------------------------------
# 入口
# -----------------------------------------------------------
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon())   # 可加载自定义图标
    MainWindow().show()
    sys.exit(app.exec_())
