"""
雨量数据读取管理器
负责雨量数据的读取、处理和展示
"""
import sys
import os
import pyodbc
import pandas as pd
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QTableWidget, QTableWidgetItem, 
    QHeaderView, QProgressDialog, QMessageBox, QPushButton, QDesktopWidget
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer

# 现代深色主题配色方案
COLORS = {
    'primary': "#2563EB", 'secondary': "#3B82F6", 'accent': "#10B981", 'success': "#059669",
    'warning': "#F59E0B", 'danger': "#EF4444", 'background': "#0F172A", 'surface': "#1E293B",
    'card': "#334155", 'border': "#475569", 'text_primary': "#F8FAFC", 
    'text_secondary': "#CBD5E1", 'text_muted': "#94A3B8"
}


class RainDataReader(QThread):
    """雨量数据读取线程"""
    progress_updated = pyqtSignal(str)  # 进度更新信号
    data_loaded = pyqtSignal(dict)      # 数据加载完成信号
    error_occurred = pyqtSignal(str)    # 错误信号

    def __init__(self, db_path):
        super().__init__()
        self.db_path = db_path

    def run(self):
        try:
            print("RainDataReader线程开始运行")
            self.progress_updated.emit("正在连接雨量数据库...")
            
            # 构建数据库连接字符串
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                fr"DBQ={self.db_path}"
            )
            print(f"连接字符串: {conn_str}")
            
            with pyodbc.connect(conn_str) as conn:
                self.progress_updated.emit("数据库连接成功，开始读取数据...")
                
                # 读取雨量站点信息
                self.progress_updated.emit("读取雨量站点信息...")
                df_station = pd.read_sql("SELECT * FROM [P-Station]", conn)
                
                # 读取降雨观测数据
                self.progress_updated.emit("读取降雨观测数据...")
                df_data = pd.read_sql("SELECT * FROM [表]", conn)

            self.progress_updated.emit("数据读取完成，正在处理...")
            
            # 数据格式转换
            station_arr = df_station.values
            data_arr = df_data.values
            
            # 按站点分组处理
            self.progress_updated.emit("正在按站名对数据进行分组...")
            key_col = 2  # 站名列
            keys = np.unique(data_arr[:, key_col])
            
            # 创建站点信息字典
            stations_info = {}
            
            for station_key in keys:
                # 获取该站点的数据记录
                station_records = data_arr[data_arr[:, key_col] == station_key]
                record_count = station_records.shape[0]
                
                # 从站点表中查找该站点的基础信息
                station_info_row = None
                for row in station_arr:
                    if str(row[2]) == str(station_key):
                        station_info_row = row
                        break
                
                stations_info[station_key] = {
                    'name': str(station_key),
                    'record_count': record_count,
                    'has_station_info': station_info_row is not None
                }

            self.progress_updated.emit("数据处理完成！")
            print(f"数据处理完成，共{len(stations_info)}个站点")
            print("准备发射data_loaded信号...")
            self.data_loaded.emit(stations_info)
            print("data_loaded信号已发射")
            
            # 强制处理事件队列
            from PyQt5.QtWidgets import QApplication
            QApplication.processEvents()
            
        except Exception as e:
            print(f"RainDataReader发生错误: {e}")
            self.error_occurred.emit(f"读取数据时发生错误：{str(e)}")


class StationInfoDialog(QDialog):
    """雨量站信息展示对话框"""
    
    def __init__(self, stations_info, parent=None):
        super().__init__(parent)
        self.stations_info = stations_info
        self.setWindowTitle(f"雨量站信息 - 共{len(stations_info)}个站点")
        self.setModal(False)  # 改为非模态对话框
        
        # 获取屏幕尺寸并设置响应式窗口
        if parent and hasattr(parent, 'screen_width'):
            screen_width = parent.screen_width
            screen_height = parent.screen_height
        else:
            screen = QDesktopWidget().screenGeometry()
            screen_width, screen_height = screen.width(), screen.height()
        
        # 计算响应式尺寸
        self.sizes = {
            'base_font': max(11, int(screen_width * 0.0095)),    # 基础字体
            'table_font': max(13, int(screen_width * 0.009)),    # 表格字体（稍微增大）
            'header_font': max(15, int(screen_width * 0.0105)), # 表头字体（稍微增大）
            'button_font': max(13, int(screen_width * 0.0095)), # 按钮字体（稍微增大）
            'padding': max(6, int(screen_width * 0.005)),        # 内边距（减小）
            'button_height': max(40, int(screen_height * 0.045)), # 按钮高度（稍微增大）
            'row_height': max(35, int(screen_height * 0.04)),    # 表格行高（增大）
        }
        
        # 设置窗口尺寸（屏幕的60%）
        dialog_width = int(screen_width * 0.6)
        dialog_height = int(screen_height * 0.7)
        self.resize(dialog_width, dialog_height)
        self.setMinimumSize(int(screen_width * 0.4), int(screen_height * 0.5))
        
        # 居中显示
        self.move((screen_width - dialog_width) // 2, (screen_height - dialog_height) // 2)
        
        # 设置窗口标志，确保窗口能正常显示
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinMaxButtonsHint)
        
        print(f"创建雨量站信息对话框，包含 {len(stations_info)} 个站点")
        
        try:
            self._init_ui()
            print("雨量站信息对话框初始化完成")
        except Exception as e:
            print(f"初始化对话框时发生错误: {e}")
    
    def closeEvent(self, event):
        """重写关闭事件"""
        print("雨量站信息对话框被关闭")
        event.accept()
    
    def showEvent(self, event):
        """重写显示事件"""
        print("雨量站信息对话框显示")
        super().showEvent(event)

    def _init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(int(self.sizes['padding'] * 1.5), int(self.sizes['padding'] * 1.5), 
                                 int(self.sizes['padding'] * 1.5), int(self.sizes['padding'] * 1.5))
        layout.setSpacing(int(self.sizes['padding'] * 1.2))
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["站点名称", "记录数量", "基础信息"])
        
        # 设置表格样式
        c = COLORS
        self.table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {c['surface']};
                color: {c['text_primary']};
                border: 1px solid {c['border']};
                border-radius: {self.sizes['padding']}px;
                font-size: {self.sizes['table_font']}px;
                gridline-color: {c['border']};
                selection-background-color: {c['primary']};
            }}
            QTableWidget::item {{
                padding: {self.sizes['padding'] * 2}px {self.sizes['padding'] * 2.5}px;
                border-bottom: 1px solid {c['border']};
                min-height: {self.sizes['row_height']}px;
                font-weight: 500;
            }}
            QTableWidget::item:selected {{
                background-color: {c['primary']};
                color: {c['text_primary']};
            }}
            QTableWidget::item:hover {{
                background-color: rgba(37, 99, 235, 0.1);
            }}
            QHeaderView::section {{
                background-color: {c['card']};
                color: {c['text_primary']};
                padding: {self.sizes['padding'] * 1.5}px;
                border: none;
                border-bottom: 2px solid {c['border']};
                font-weight: 700;
                font-size: {self.sizes['header_font']}px;
                min-height: {self.sizes['row_height'] * 1.3}px;
                text-align: center;
            }}
            QHeaderView::section:hover {{
                background-color: {c['border']};
            }}
        """)
        
        # 填充数据
        self._populate_table()
        
        # 设置表格属性和列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 站点名称列自适应
        
        # 设置后两列的固定宽度，增加间距
        # 获取屏幕宽度用于计算列宽
        if hasattr(self, 'parent') and self.parent():
            screen_w = self.parent().screen_width
        else:
            screen_w = QDesktopWidget().screenGeometry().width()
            
        self.table.setColumnWidth(1, max(120, int(screen_w * 0.08)))  # 记录数量列
        self.table.setColumnWidth(2, max(100, int(screen_w * 0.06)))  # 基础信息列
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Fixed)
        
        layout.addWidget(self.table)
        
        # 添加关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.setMinimumHeight(self.sizes['button_height'])
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 {c['primary']}, stop:1 {c['secondary']});
                color: {c['text_primary']};
                border: 1px solid {c['primary']};
                border-radius: {self.sizes['padding']}px;
                padding: {self.sizes['padding']}px {self.sizes['padding'] * 2}px;
                font-size: {self.sizes['button_font']}px;
                font-weight: 600;
                margin: {self.sizes['padding'] // 2}px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 {c['secondary']}, stop:1 {c['primary']});
                border: 1px solid {c['secondary']};
            }}
            QPushButton:pressed {{
                background: {c['card']};
                border: 1px solid {c['border']};
            }}
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

    def _populate_table(self):
        """填充表格数据"""
        # 按记录数量排序
        sorted_stations = sorted(
            self.stations_info.items(), 
            key=lambda x: x[1]['record_count'], 
            reverse=True
        )
        
        self.table.setRowCount(len(sorted_stations))
        
        for row, (station_name, info) in enumerate(sorted_stations):
            # 站点名称
            name_item = QTableWidgetItem(str(station_name))
            self.table.setItem(row, 0, name_item)
            
            # 记录数量
            count_item = QTableWidgetItem(f"{info['record_count']:,}")
            self.table.setItem(row, 1, count_item)
            
            # 基础信息状态
            status_item = QTableWidgetItem("✓" if info['has_station_info'] else "✗")
            self.table.setItem(row, 2, status_item)


class RainDataManager:
    """雨量数据管理器"""
    
    def __init__(self, parent_window):
        self.parent = parent_window
    
    def load_data(self, db_path):
        """加载雨量数据"""
        try:
            print(f"开始自动加载雨量数据: {db_path}")
            
            # 创建进度对话框
            self.progress_dialog = QProgressDialog("正在读取雨量数据...", "取消", 0, 0, self.parent)
            self.progress_dialog.setWindowTitle("雨量数据读取")
            self.progress_dialog.setModal(True)
            self.progress_dialog.show()
            print("进度对话框已显示")
            
            # 创建并启动数据读取线程
            self.rain_reader = RainDataReader(db_path)
            self.rain_reader.progress_updated.connect(self._update_progress, Qt.QueuedConnection)
            self.rain_reader.data_loaded.connect(self._on_data_loaded, Qt.QueuedConnection)
            self.rain_reader.error_occurred.connect(self._on_error, Qt.QueuedConnection)
            self.rain_reader.finished.connect(self._on_reading_finished, Qt.QueuedConnection)
            print("信号连接完成（使用QueuedConnection）")
            self.rain_reader.start()
            print("数据读取线程已启动")
            
            # 连接取消按钮
            self.progress_dialog.canceled.connect(self._cancel_reading)
            
        except Exception as e:
            print(f"启动雨量数据读取时发生错误: {e}")
            QMessageBox.critical(self.parent, "错误", f"启动雨量数据读取时发生错误: {str(e)}")
    
    def _update_progress(self, message):
        """更新进度信息"""
        print(f"进度更新: {message}")
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.setLabelText(message)
    
    def _on_data_loaded(self, stations_info):
        """数据加载完成"""
        try:
            print(f"_on_data_loaded被调用，收到{len(stations_info)}个站点数据")
            
            # 关闭进度对话框
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None
            
            # 使用QTimer延迟创建对话框，避免线程冲突
            QTimer.singleShot(100, lambda: self._create_station_dialog(stations_info))
            
        except Exception as e:
            print(f"显示数据时发生错误: {e}")
            QMessageBox.critical(self.parent, "错误", f"显示数据时发生错误: {str(e)}")
    
    def _create_station_dialog(self, stations_info):
        """在主线程中创建站点信息对话框"""
        try:
            # 创建并显示站点信息对话框
            self.station_dialog = StationInfoDialog(stations_info, self.parent)
            
            # 确保对话框不会被垃圾回收
            self.station_dialog.setAttribute(Qt.WA_DeleteOnClose, False)
            
            # 显示对话框
            self.station_dialog.show()
            self.station_dialog.raise_()
            self.station_dialog.activateWindow()
            
            print(f"成功显示雨量站信息窗口，包含 {len(stations_info)} 个站点")
            
        except Exception as e:
            print(f"创建对话框时发生错误: {e}")
            QMessageBox.critical(self.parent, "错误", f"创建对话框时发生错误: {str(e)}")
    
    def _on_error(self, error_message):
        """处理错误"""
        print(f"雨量数据读取错误: {error_message}")
        QMessageBox.critical(self.parent, "错误", error_message)
    
    def _on_reading_finished(self):
        """读取完成"""
        # 清理线程引用
        if hasattr(self, 'rain_reader'):
            self.rain_reader.deleteLater()
            delattr(self, 'rain_reader')
    
    def _cancel_reading(self):
        """取消读取"""
        if hasattr(self, 'rain_reader') and self.rain_reader.isRunning():
            self.rain_reader.terminate()
            self.rain_reader.wait()
