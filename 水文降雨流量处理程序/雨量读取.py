import pyodbc
import pandas as pd
import numpy as np
from pathlib import Path

# ▶ 1. 数据库绝对路径（如有多库，可放入循环或写成变量）
db_path = Path(r"C:\Users\<USER>\Desktop\雨量数据.accdb")

# ▶ 2. 构造连接字符串
conn_str = (
    r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
    fr"DBQ={db_path}"
)

# ▶ 3. 读取数据
with pyodbc.connect(conn_str) as conn:
    # 3-1 读取 P-Station 表（表名包含连字符需要用方括号括起来）
    df_station = pd.read_sql("SELECT * FROM [P-Station]", conn)

    # 3-2 读取业务数据表（示例用 RainData；请改成实际表名）
    df_data = pd.read_sql("SELECT * FROM [表]", conn)

# ▶ 4. 转成合适的 Numpy 数组（也可保持 DataFrame 形式）
station_arr: np.ndarray = df_station.values          # 站点表 → station_arr
data_arr:    np.ndarray = df_data.values             # 数据表 → data_arr

# ---------- 参数 ----------
key_col = 2               # 2 = 站名，1 = 站码
key_desc = "站名" if key_col == 2 else "站码"

# ---------- 1) 提取唯一键值 ----------
keys = np.unique(data_arr[:, key_col])     # e.g. ['上苇甸' '下苇甸' ...] 或 ['30746600' ...]

# ---------- 2) 建立 {键 : 子数组} ----------
station_data = {k: data_arr[data_arr[:, key_col] == k] for k in keys}

# ---------- 3) (可选) 打印每站条数 ----------
print(f"按 {key_desc} 拆分后的记录统计：")
for k, arr in station_data.items():
    print(f"{k:<10s}: {arr.shape[0]:>6d} 条")
