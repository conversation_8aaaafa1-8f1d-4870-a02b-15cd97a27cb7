# -*- coding: utf-8 -*-
"""
月潭流域 LSTM 训练数据自动化预处理脚本（含下一小时降雨+蒸发作为输入）
------------------------------------------------------------
1. 读取 summary / data / evapd 三张 Sheet
2. 线性插值补缺 + MinMax 归一化
3. 生成监督学习样本：
     ▶ X_hist: (look_back, F)  — 历史 look_back 小时的所有特征
     ▶ X_exog: (R+1,)         — 下一小时的 R 站降雨 + 1 列蒸发
     ▶ y:      ()            — 下一小时的流量
4. 按洪水场次顺序做 8:2 划分
5. 保存 train/test npz 以及 scaler.pkl
"""

import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.preprocessing import MinMaxScaler
import pickle

# ========== ★ 用户根据实际情况修改 ↓↓↓ =========================================
xls_path   = Path(r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903"
                  r"\安徽中小流域资料整理 - 给孟涵 - 20230903\月潭次模率定.xls")
out_dir    = Path(r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903\预处理输出")
look_back  = 6            # LSTM 想看的历史小时数
target_idx = 0            # 预测目标列（0 = 流量）
# ==============================================================================

out_dir.mkdir(parents=True, exist_ok=True)

# === 1. 读取 3 张 Sheet ========================================================
summary_df = pd.read_excel(xls_path, sheet_name="summary", engine="xlrd")
data_df    = pd.read_excel(xls_path, sheet_name="data",    engine="xlrd")
evap_df    = pd.read_excel(xls_path, sheet_name="evapd",   engine="xlrd")

# === 2. 时间字段预处理 & 小时展平蒸发 =================================================
def int2dt(s: pd.Series, unit: str) -> pd.DatetimeIndex:
    fmt = "%Y%m%d%H" if unit.upper()=="H" else "%Y%m%d"
    return pd.to_datetime(s.astype(str).str.zfill(len(fmt)-2), format=fmt)

# -- data sheet -----------------------------------------------------------------
data_df["ts"] = int2dt(data_df.iloc[:,0], "H")
data_df.set_index("ts", inplace=True)

# -- evap sheet -----------------------------------------------------------------
evap_df["date"] = int2dt(evap_df.iloc[:,0], "D")
evap_df.set_index("date", inplace=True)
evap_df.drop(columns=evap_df.columns[0], inplace=True)
evap_hourly = (
    evap_df
    .resample("h")
    .ffill()
    .rename(columns={evap_df.columns[0]: "evap"})
)

# === 3. 生成特征矩阵 ===========================================================
flow_col  = data_df.columns[1]
rain_cols = data_df.columns[2:]
feature_df = data_df[[flow_col, *rain_cols]].join(evap_hourly["evap"], how="left")

# === 4. 按场次切片并存 dict ======================================================
events = {}
for _, row in summary_df.iterrows():
    eid        = row.iloc[0]
    t0         = pd.to_datetime(str(int(row.iloc[1])), format="%Y%m%d%H")
    t1         = pd.to_datetime(str(int(row.iloc[3])), format="%Y%m%d%H")
    seg        = feature_df.loc[t0:t1]
    if seg.empty:
        print(f"[警告] 场次 {eid} 无有效记录，已跳过")
    else:
        events[eid] = seg.to_numpy(dtype=np.float32)
print(f"✔ 解析完成：共 {len(events)} 场次")

# === 5. 插值补缺 ================================================================
events_clean = {
    eid: pd.DataFrame(arr)
             .interpolate(method="linear")
             .ffill().bfill()
             .to_numpy(dtype=np.float32)
    for eid, arr in events.items()
}

# === 6. MinMax 归一化 ===========================================================
all_data = np.vstack(list(events_clean.values()))
scaler   = MinMaxScaler(feature_range=(0, 1)).fit(all_data)
events_scaled = {eid: scaler.transform(arr) for eid, arr in events_clean.items()}

# === 7. 滑动窗口 + 下一小时降雨/蒸发 as exog =======================================
# 找到降雨和蒸发在列中的索引
F        = next(iter(events_scaled.values())).shape[1]
rain_idx = list(range(1, 1+len(rain_cols)))
evap_idx = rain_idx[-1] + 1

# 按洪水场次顺序划分 8:2
eids       = sorted(events_scaled.keys())
n_train    = int(len(eids) * 0.8)
train_eids = eids[:n_train]
test_eids  = eids[n_train:]

def build_X_e_y(eid_list):
    X_hist, X_exog, y_list = [], [], []
    for eid in eid_list:
        arr = events_scaled[eid]
        T   = arr.shape[0]
        for i in range(T - look_back):
            hist = arr[i:i+look_back]                                    # (look_back, F)
            exog = np.hstack([arr[i+look_back, rain_idx],
                              arr[i+look_back, evap_idx]])              # (R+1,)
            target = arr[i+look_back, target_idx]                        # 下一小时流量
            X_hist.append(hist)
            X_exog.append(exog)
            y_list.append(target)
    return np.stack(X_hist), np.stack(X_exog), np.array(y_list)

Xh_tr, Xe_tr, y_tr = build_X_e_y(train_eids)
Xh_te, Xe_te, y_te = build_X_e_y(test_eids)

print(f"训练集：X_hist {Xh_tr.shape}, X_exog {Xe_tr.shape}, y {y_tr.shape}")
print(f"测试集：X_hist {Xh_te.shape}, X_exog {Xe_te.shape}, y {y_te.shape}")

# === 8. 保存 ================================================================
train_path = out_dir / f"train_look{look_back}.npz"
test_path  = out_dir / f"test_look{look_back}.npz"

np.savez_compressed(train_path,
                    X_hist=Xh_tr, X_exog=Xe_tr, y=y_tr)
np.savez_compressed(test_path,
                    X_hist=Xh_te, X_exog=Xe_te, y=y_te)
print(f"✔ 训练集已保存：{train_path}")
print(f"✔ 测试集已保存：{test_path}")

scaler_path = out_dir / "minmax_scaler.pkl"
with open(scaler_path, "wb") as f:
    pickle.dump(scaler, f)
print(f"✔ scaler 已保存：{scaler_path}")
