# -*- coding: utf-8 -*-
"""
LSTM 推理 + 按洪水场次绘图  （PaddlePaddle 2.x）
──────────────────────────────────────────────
★ 功能流程
1. 读取权重文件 lstm_with_exog.pdparams，自动推断 hidden_size /
   input_dim / num_layers，重建 LSTMWithExog 模型并加载权重
2. 读取原始 Excel（summary / data / evapd），走一次与你训练时
   完全一致的预处理 → 得到每场次的标准化矩阵
3. 滑动窗口推理：hist(6 h) + [next-hour rain总量, evap] → 下一小时流量
4. 反归一到实测单位，逐场次绘线并保存 PNG
──────────────────────────────────────────────
"""

import warnings
warnings.filterwarnings("ignore")

# ===== 0. 路径（★ 如需变更只改这里） =========================================
from pathlib import Path

BASE_DIR   = Path(r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903")
XLS_PATH   = BASE_DIR / r"安徽中小流域资料整理 - 给孟涵 - 20230903\月潭次模率定.xls"
OUT_DIR    = BASE_DIR / "预处理输出"
MODEL_WEIGHTS = OUT_DIR / "lstm_with_exog.pdparams"
SCALER_PKL    = OUT_DIR / "minmax_scaler.pkl"
PLOT_DIR      = OUT_DIR / "plots_final"
LOOK_BACK     = 6                 # 必须与训练保持一致
EXOG_DIM      = 2                 # [下一小时雨量总和, 下一小时蒸发]
# ============================================================================

# ---------------- 1. 常用包 --------------------------------------------------
import numpy as np
import pandas as pd
import pickle, os
import paddle
import paddle.nn as nn
from tqdm import tqdm
import matplotlib.pyplot as plt

PLOT_DIR.mkdir(parents=True, exist_ok=True)

# ---------------- 2. 定义网络结构 -------------------------------------------
class LSTMWithExog(nn.Layer):
    def __init__(self, input_dim: int, hidden_size: int,
                 num_layers: int, exog_dim: int = 2):
        super().__init__()
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_size,
            num_layers=num_layers,
            direction="forward"
        )
        self.fc = nn.Sequential(
            nn.Linear(hidden_size + exog_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )

    def forward(self, hist, exog):
        """
        hist: (batch, LOOK_BACK, F)
        exog: (batch, exog_dim)
        """
        hist = paddle.transpose(hist, perm=[1, 0, 2])   # (seq,batch,F)
        out, _ = self.lstm(hist)
        last = out[-1]                                   # (batch, hidden)
        x = paddle.concat([last, exog], axis=1)
        y_hat = self.fc(x)
        return y_hat.squeeze(1)


# ---------------- 3. 权重 → 自动推断网络超参 ------------------------------
state_dict = paddle.load(str(MODEL_WEIGHTS))
fc_in_feats = state_dict["fc.0.weight"].shape[0]     # hidden + exog
hidden_size = fc_in_feats - EXOG_DIM
lstm_w_ih = [k for k in state_dict if k.startswith("lstm.weight_ih_l")]
num_layers = len(lstm_w_ih)
input_dim  = state_dict[lstm_w_ih[0]].shape[1]

print(f"→ 自动解析网络参数：input_dim={input_dim}, hidden_size={hidden_size}, "
      f"num_layers={num_layers}, exog_dim={EXOG_DIM}")

# ---------------- 4. 实例化模型 & 加载权重 ------------------------------
model = LSTMWithExog(input_dim, hidden_size, num_layers, EXOG_DIM)
model.set_state_dict(state_dict)
model.eval()
print(f"✔ 权重已加载：{MODEL_WEIGHTS}\n")

# ---------------- 5. 读 scaler 用于反归一 -------------------------------
with open(SCALER_PKL, "rb") as f:
    scaler = pickle.load(f)
flow_min, flow_max = scaler.data_min_[0], scaler.data_max_[0]  # 第 0 列 = 流量

# ---------------- 6. 读取 Excel 并复现预处理 ----------------------------
def int2dt(series: pd.Series, unit="H"):
    fmt = "%Y%m%d%H" if unit.upper() == "H" else "%Y%m%d"
    return pd.to_datetime(series.astype(str).str.zfill(len(fmt) - 2), format=fmt)

summary_df = pd.read_excel(XLS_PATH, sheet_name="summary", engine="xlrd")
data_df    = pd.read_excel(XLS_PATH, sheet_name="data",    engine="xlrd")
evap_df    = pd.read_excel(XLS_PATH, sheet_name="evapd",   engine="xlrd")

data_df["ts"]  = int2dt(data_df.iloc[:,0], "H"); data_df.set_index("ts", inplace=True)
evap_df["date"] = int2dt(evap_df.iloc[:,0], "D"); evap_df.set_index("date", inplace=True)
evap_df.drop(columns=evap_df.columns[0], inplace=True)

evap_hourly = (evap_df.resample("h").ffill()
               .rename(columns={evap_df.columns[0]:"evap"}))

flow_col  = data_df.columns[1]      # 流量
rain_cols = data_df.columns[2:]     # 多站降雨
feature_df = data_df[[flow_col, *rain_cols]].join(evap_hourly["evap"], how="left")

# ---------------- 7. 按场次推理并绘图 -----------------------------------
print("—— 开始逐场次推理与绘图 ——")
errs = []
for _, row in summary_df.iterrows():
    eid        = str(int(row.iloc[0]))
    t0         = pd.to_datetime(str(int(row.iloc[1])), format="%Y%m%d%H")
    t1         = pd.to_datetime(str(int(row.iloc[3])), format="%Y%m%d%H")
    seg_df     = feature_df.loc[t0:t1]

    if seg_df.shape[0] <= LOOK_BACK:
        print(f"跳过 {eid} — 数据点不足")
        continue

    # —— 插值补缺 → ndarray → 标准化
    seg_filled = seg_df.interpolate("linear").ffill().bfill().to_numpy(np.float32)
    seg_scaled = scaler.transform(seg_filled)

    # —— 生成滑窗样本 & exog
    X_hist, X_exog, y_obs = [], [], []
    rain_slice = slice(1, 1+len(rain_cols))      # 所有雨量列
    evap_idx   = -1                               # evapo 列

    for i in range(LOOK_BACK, seg_scaled.shape[0]):
        hist = seg_scaled[i-LOOK_BACK:i, :]
        rain_next = seg_scaled[i, rain_slice].sum()
        evap_next = seg_scaled[i, evap_idx]
        exog = np.asarray([rain_next, evap_next], dtype=np.float32)
        target = seg_scaled[i, 0]                 # 流量 (已归一化)

        X_hist.append(hist)
        X_exog.append(exog)
        y_obs.append(target)

    X_hist = paddle.to_tensor(np.stack(X_hist))
    X_exog = paddle.to_tensor(np.stack(X_exog))
    y_obs  = np.asarray(y_obs)

    with paddle.no_grad():
        y_pred = model(X_hist, X_exog).numpy()

    # —— 反归一：scaled → original unit
    y_pred_real = y_pred*(flow_max-flow_min) + flow_min
    y_obs_real  = y_obs *(flow_max-flow_min) + flow_min

    rmse = np.sqrt(np.mean((y_pred_real - y_obs_real)**2))
    errs.append(rmse)

    # —— 绘图
    plt.figure(figsize=(10,4))
    plt.plot(y_obs_real, label="Observed")
    plt.plot(y_pred_real, label="Simulated")
    plt.title(f"Event {eid}  |  RMSE={rmse:.3f}")
    plt.xlabel("Time step (h)")
    plt.ylabel("Discharge (m³/s)")
    plt.legend()
    plt.tight_layout()
    png_path = PLOT_DIR / f"event_{eid}.png"
    plt.savefig(png_path, dpi=160)
    plt.close()
    print(f"  · 完成 {eid}  →  {png_path}")

# ---------------- 8. 统计整体误差 ---------------------------------------
if errs:
    print(f"\n✔ 共绘制 {len(errs)} 场，平均 RMSE = {np.mean(errs):.3f}")
    print(f"✔ 所有 PNG 已保存至：{PLOT_DIR}")

