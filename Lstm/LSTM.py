# -*- coding: utf-8 -*-
"""
LSTM（PaddlePaddle 2.x）训练脚本
-----------------------------------------------------------
读取预处理生成的 train_look6.npz / test_look6.npz
  * X_hist : (N, 6, F_in)        —— 历史 6 小时全部特征
  * X_exog : (N, exog_dim)       —— 下一小时[各站降雨…, 蒸发]
  * y      : (N,)                —— 下一小时流量
"""

import os
import numpy as np
import paddle
import paddle.nn as nn
from paddle.io import Dataset, DataLoader

# ---------- tqdm 可选依赖 ----------
try:
    from tqdm import tqdm                    # 进度条
except ModuleNotFoundError:                  # 没装 tqdm → 用普通迭代
    def tqdm(iterable, *args, **kwargs):
        return iterable
# -----------------------------------

# ========= ★ 根据实际情况改路径 ↓↓↓ ===========================================
DATA_DIR  = (r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903"
             r"\预处理输出")
TRAIN_NPZ = os.path.join(DATA_DIR, "train_look6.npz")
TEST_NPZ  = os.path.join(DATA_DIR,  "test_look6.npz")

LOOK_BACK     = 6           # 历史窗口
BATCH_SIZE    = 64
NUM_EPOCHS    = 40
LEARNING_RATE = 5e-4
HIDDEN_SIZE   = 128         # LSTM 隐层
NUM_LAYERS    = 2
# ==============================================================================


# ------------------------- 数据集封装 -----------------------------------------
class FloodSeqDataset(Dataset):
    def __init__(self, npz_path: str):
        npz = np.load(npz_path)
        self.X_hist = npz["X_hist"].astype("float32")    # (N, 6, F_in)
        self.X_exog = npz["X_exog"].astype("float32")    # (N, exog_dim)
        self.y      = npz["y"].astype("float32")         # (N,)

    def __len__(self):
        return self.y.shape[0]

    def __getitem__(self, idx):
        # 返回三个张量：hist, exog, y
        return (self.X_hist[idx], self.X_exog[idx], self.y[idx])


# --------------------------- 模型结构 -----------------------------------------
class LSTMWithExog(nn.Layer):
    """
    输入 = [历史序列, 下一小时外生量] →
    先用 LSTM 提取历史序列特征，再拼接外生量送入全连接。
    """

    def __init__(self, input_dim: int, hidden_size: int,
                 num_layers: int, exog_dim: int):
        super().__init__()
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_size,
            num_layers=num_layers,
            direction="forward"
        )
        self.fc = nn.Sequential(
            nn.Linear(hidden_size + exog_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )

    def forward(self, hist, exog):
        """
        hist: (batch, LOOK_BACK, F_in)
        exog: (batch, exog_dim)
        """
        hist = paddle.transpose(hist, perm=[1, 0, 2])   # → (seq, batch, F_in)
        output, _ = self.lstm(hist)                     # output: (seq, batch, hidden)
        last_out  = output[-1]                          # 取最后时刻
        x = paddle.concat([last_out, exog], axis=1)     # 拼接外生量
        y_hat = self.fc(x)
        return y_hat.squeeze(1)


# ---------------------------- 训练脚本 ----------------------------------------
def main():

    # ---------- 1. 数据加载 ----------
    train_ds = FloodSeqDataset(TRAIN_NPZ)
    test_ds  = FloodSeqDataset(TEST_NPZ)

    train_loader = DataLoader(train_ds, batch_size=BATCH_SIZE,
                              shuffle=True, drop_last=True)
    test_loader  = DataLoader(test_ds,  batch_size=BATCH_SIZE,
                              shuffle=False)

    # ---------- 2. 创建模型 ----------
    INPUT_DIM = train_ds.X_hist.shape[-1]     # 特征维度
    EXOG_DIM  = train_ds.X_exog.shape[-1]     # 动态获取外生维度

    model = LSTMWithExog(INPUT_DIM, HIDDEN_SIZE, NUM_LAYERS, exog_dim=EXOG_DIM)
    mse   = nn.MSELoss()
    opt   = paddle.optimizer.Adam(learning_rate=LEARNING_RATE,
                                  parameters=model.parameters())

    # ---------- 3. 训练 ----------
    for epoch in range(1, NUM_EPOCHS + 1):
        model.train()
        train_iter = tqdm(train_loader, desc=f"Epoch {epoch}/{NUM_EPOCHS}")
        for hist, exog, y in train_iter:
            y_pred = model(hist, exog)
            loss   = mse(y_pred, y)
            loss.backward()
            opt.step()
            opt.clear_gradients()
            train_iter.set_postfix({"train_loss": f"{loss.item():.6f}"})

        # ------- 4. 验证 -------
        model.eval()
        with paddle.no_grad():
            losses = []
            for hist, exog, y in test_loader:
                y_pred = model(hist, exog)
                losses.append(mse(y_pred, y).item())  # ← 这里改成 .item()
            val_loss = float(np.mean(losses))

        print(f"Epoch {epoch:>2d}/{NUM_EPOCHS}  "
              f"ValLoss={val_loss:.6f}")

    # ---------- 5. 保存 ----------
    save_path = os.path.join(DATA_DIR, "lstm_with_exog.pdparams")
    paddle.save(model.state_dict(), save_path)
    print(f"\n✔ 模型参数已保存：{save_path}")


if __name__ == "__main__":
    paddle.set_device("gpu" if paddle.is_compiled_with_cuda() else "cpu")
    main()
