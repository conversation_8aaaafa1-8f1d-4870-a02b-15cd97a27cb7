import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.patches import Rectangle
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

# 查询系统可用中文字体
font_list = fm.findSystemFonts(fontext='ttf')
chinese_fonts = []
for font_path in font_list:
    prop = fm.FontProperties(fname=font_path)
    try:
        name = prop.get_name()
    except:
        continue
    if any(keyword in name for keyword in [
        'Song', 'Hei', 'YaHei', 'SimSun',
        'Microsoft YaHei', 'STFangsong',
        'PingFang', 'Noto Sans'
    ]):
        chinese_fonts.append((name, font_path))

# 设置字体，若无中文字体则回退 sans-serif
if chinese_fonts:
    plt.rcParams['font.sans-serif'] = [chinese_fonts[0][0]]
else:
    plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['axes.unicode_minus'] = False

# 节次到具体时间映射
period_times = {
    '1-2': (8.00, 9 + 35/60),          # 第1-2节 08:00–09:35
    '3-5': (9 + 50/60, 12 + 15/60),     # 第3-5节 09:50–12:15
    '6-8': (14.00, 16 + 35/60),         # 第6-8节 14:00–16:35
    '10-12': (18 + 30/60, 20 + 55/60),  # 第10-12节 18:30–20:55
    '13': (21.00, 21 + 45/60),          # 第13节 21:00–21:45
}

# 周二事件列表，包含新增内容
events = [
    {'period': '1-2',   'label': '整理数据'},
    {'period': '3-5',   'label': '给老师送东西'},
    {'period': '6-8',   'label': '学习PaddleTS'},
    {'period': '10-12', 'label': '完成深度学习作业'},
    {'period': '13',    'label': '锻炼'},
]

# 绘图
fig, ax = plt.subplots(figsize=(4, 9))
for evt in events:
    start, end = period_times[evt['period']]
    rect = Rectangle((-0.3, start), 0.6, end - start,
                     edgecolor='black', facecolor='skyblue', alpha=0.6)
    ax.add_patch(rect)
    ax.text(0, (start + end) / 2, evt['label'], ha='center', va='center', fontsize=12)

# 添加每小时辅助横线
for hour in range(8, 25):
    ax.axhline(y=hour, color='gray', linestyle='--', linewidth=0.5, alpha=0.7)

# 坐标轴设置
ax.set_xlim(-0.5, 0.5)
ax.set_ylim(8, 24)  # 08:00–24:00 区间
ax.invert_yaxis()
ax.set_xticks([0])
ax.set_xticklabels(['周二'], fontsize=14)
ax.set_yticks(list(range(8, 25, 2)))
ax.set_yticklabels([f'{h:02d}:00' for h in range(8, 25, 2)], fontsize=12)

ax.set_title('第14周 周二 课程详情', fontsize=16)
ax.set_ylabel('时间', fontsize=12)

plt.tight_layout()
plt.show()
