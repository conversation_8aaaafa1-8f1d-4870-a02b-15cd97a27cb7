import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.patches import Rectangle
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

# 查询系统可用中文字体
font_list = fm.findSystemFonts(fontext='ttf')
chinese_fonts = []
for font_path in font_list:
    prop = fm.FontProperties(fname=font_path)
    try:
        name = prop.get_name()
    except:
        continue
    # 筛选常见中文字体
    if any(keyword in name for keyword in [
        'Song', 'Hei', 'YaHei', 'SimSun',
        'Microsoft YaHei', 'STFangsong',
        'PingFang', 'Noto Sans'
    ]):
        chinese_fonts.append((name, font_path))

# 设置字体，若无中文字体则回退 sans-serif
if chinese_fonts:
    plt.rcParams['font.sans-serif'] = [chinese_fonts[0][0]]
else:
    plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['axes.unicode_minus'] = False

# 定义事件及其时间（十进制小时）
events = [
    {'start': 7.00, 'end': 7 + 45/60, 'label': '早自习：安排6月计划'},
    {'start': 8.00, 'end': 9 + 35/60, 'label': '整理数据'},
    {'start': 9 + 50/60, 'end': 12 + 15/60, 'label': '学习PaddleTS'},
    {'start': 14.00, 'end': 16 + 35/60, 'label': '学习日语'},
]

# 绘制周二任务详情
fig, ax = plt.subplots(figsize=(4, 8))
for evt in events:
    rect = Rectangle((-0.3, evt['start']), 0.6, evt['end'] - evt['start'],
                     edgecolor='black', facecolor='skyblue', alpha=0.6)
    ax.add_patch(rect)
    ax.text(0, (evt['start'] + evt['end']) / 2, evt['label'], ha='center', va='center', fontsize=12)

# 添加每小时辅助横线
for hour in range(7, 19):
    ax.axhline(y=hour, color='gray', linestyle='--', linewidth=0.5, alpha=0.7)

# 坐标轴设置
ax.set_xlim(-0.5, 0.5)
ax.set_ylim(7, 18)  # 显示 07:00–18:00 区间
ax.invert_yaxis()
ax.set_xticks([0])
ax.set_xticklabels(['周二'], fontsize=14)
ax.set_yticks(list(range(7, 19, 2)))
ax.set_yticklabels([f'{h:02d}:00' for h in range(7, 19, 2)], fontsize=12)

# 标题和标签
ax.set_title('第15周 周二 课程详情', fontsize=16)
ax.set_ylabel('时间', fontsize=12)

plt.tight_layout()
plt.show()
