import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.patches import Rectangle
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

# 查询系统可用中文字体
font_list = fm.findSystemFonts(fontext='ttf')
chinese_fonts = []
for font_path in font_list:
    prop = fm.FontProperties(fname=font_path)
    try:
        name = prop.get_name()
    except:
        continue
    # 筛选可能的中文字体
    if any(keyword in name for keyword in ['Song', 'Hei', 'YaHei', 'SimSun', 'Microsoft YaHei', 'STFangsong', 'PingFang', 'Noto Sans']):
        chinese_fonts.append((name, font_path))

# 设置字体，若无中文字体则使用sans-serif
if chinese_fonts:
    plt.rcParams['font.sans-serif'] = [chinese_fonts[0][0]]
else:
    plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['axes.unicode_minus'] = False

# 节次到具体时间映射（十进制小时）
period_times = {
    '1': (8.00, 8 + 45/60),            # 第1节 08:00–08:45
    '2': (8 + 50/60, 9 + 35/60),       # 第2节 08:50–09:35
    '3-5': (9 + 50/60, 12 + 15/60),    # 第3-5节 09:50–12:15
    '6-8': (14.00, 16 + 35/60),        # 第6-8节 14:00–16:35
    '10-12': (18 + 30/60, 20 + 55/60), # 第10-12节 18:30–20:55
    '13': (21.00, 21 + 45/60),         # 第13节 21:00–21:45
}

# 周一事件列表，3-5 节合并为学习飞桨平台
events = [
    {'period': '1',    'label': 'SHAP值分析'},
    {'period': '2',    'label': '数据处理1'},
    {'period': '3-5',  'label': '学习飞桨平台'},
    {'period': '6-8',  'label': '论文指导写作'},
    {'period': '10-12','label': '日语'},
    {'period': '13',   'label': '锻炼'},
]

# 绘制周一课程详情
fig, ax = plt.subplots(figsize=(4, 9))
for evt in events:
    start, end = period_times[evt['period']]
    rect = Rectangle((-0.3, start), 0.6, end - start,
                     edgecolor='black', facecolor='skyblue', alpha=0.6)
    ax.add_patch(rect)
    ax.text(0, (start + end) / 2, evt['label'], ha='center', va='center', fontsize=12)

# 坐标轴设置
ax.set_xlim(-0.5, 0.5)
ax.set_ylim(8, 24)
ax.invert_yaxis()
ax.set_xticks([0])
ax.set_xticklabels(['周一'], fontsize=14)
ax.set_yticks(list(range(8, 25, 2)))
ax.set_yticklabels([f'{h:02d}:00' for h in range(8, 25, 2)], fontsize=12)

# 增加网格
ax.grid(True, linestyle='--', linewidth=0.5, alpha=0.7)

ax.set_title('第14周 周一 课程详情', fontsize=16)
ax.set_ylabel('时间', fontsize=12)

plt.tight_layout()
plt.show()
